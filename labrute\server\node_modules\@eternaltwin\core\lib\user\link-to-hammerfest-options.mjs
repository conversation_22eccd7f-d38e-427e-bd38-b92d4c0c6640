import { TaggedUnionType } from "kryo/tagged-union";
import { $LinkToHammerfestWithCredentialsOptions } from "./link-to-hammerfest-with-credentials-options.mjs";
import { $LinkToHammerfestWithRefOptions } from "./link-to-hammerfest-with-ref-options.mjs";
import { $LinkToHammerfestWithSessionKeyOptions } from "./link-to-hammerfest-with-session-key-options.mjs";
export const $LinkToHammerfestOptions = new TaggedUnionType({
    variants: [$LinkToHammerfestWithCredentialsOptions, $LinkToHammerfestWithRefOptions, $LinkToHammerfestWithSessionKeyOptions],
    tag: "method",
});
//# sourceMappingURL=link-to-hammerfest-options.mjs.map