import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $Url } from "../core/url.mjs";
import { $OauthClientInputRef } from "./oauth-client-input-ref.mjs";
import { $OauthResponseType } from "./oauth-response-type.mjs";
import { $OauthScopeString } from "./oauth-scope-string.mjs";
import { $OauthState } from "./oauth-state.mjs";
export const $OauthAuthorizationRequest = new RecordType({
    properties: {
        clientId: { type: $OauthClientInputRef },
        redirectUri: { type: $Url, optional: true },
        responseType: { type: $OauthResponseType },
        scope: { type: $OauthScopeString, optional: true },
        state: { type: $OauthState, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=oauth-authorization-request.mjs.map