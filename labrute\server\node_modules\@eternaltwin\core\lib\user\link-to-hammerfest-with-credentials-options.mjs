import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $HammerfestPassword } from "../hammerfest/hammerfest-password.mjs";
import { $HammerfestServer } from "../hammerfest/hammerfest-server.mjs";
import { $HammerfestUsername } from "../hammerfest/hammerfest-username.mjs";
import { $LinkToHammerfestMethod, LinkToHammerfestMethod } from "./link-to-hammerfest-method.mjs";
import { $UserId } from "./user-id.mjs";
export const $LinkToHammerfestWithCredentialsOptions = new RecordType({
    properties: {
        method: { type: new LiteralType({ type: $LinkToHammerfestMethod, value: LinkToHammerfestMethod.Credentials }) },
        userId: { type: $UserId },
        hammerfestServer: { type: $HammerfestServer },
        hammerfestUsername: { type: $HammerfestUsername },
        hammerfestPassword: { type: $HammerfestPassword },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=link-to-hammerfest-with-credentials-options.mjs.map