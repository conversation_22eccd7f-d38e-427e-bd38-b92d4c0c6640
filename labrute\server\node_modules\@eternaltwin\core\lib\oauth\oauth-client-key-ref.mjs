import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $OauthClientId } from "./oauth-client-id.mjs";
import { $OauthClientKey } from "./oauth-client-key.mjs";
export const $OauthClientKeyRef = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.OauthClient }) },
        id: { type: $OauthClientId, optional: true },
        key: { type: $OauthClientKey },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=oauth-client-key-ref.mjs.map