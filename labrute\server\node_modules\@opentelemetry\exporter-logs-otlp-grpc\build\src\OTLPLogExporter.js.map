{"version": 3, "file": "OTLPLogExporter.js", "sourceRoot": "", "sources": ["../../src/OTLPLogExporter.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,oFAIgD;AAChD,sEAAyE;AACzE,0EAAqE;AAErE;;GAEG;AACH,MAAa,eACX,SAAQ,qCAAqC;IAG7C,YAAY,SAAqC,EAAE;QACjD,KAAK,CACH,IAAA,sDAA4B,EAC1B,IAAA,sDAA4B,EAAC,MAAM,EAAE,MAAM,CAAC,EAC5C,yCAAsB,EACtB,mBAAmB,EACnB,2DAA2D,CAC5D,CACF,CAAC;IACJ,CAAC;CACF;AAdD,0CAcC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { LogRecordExporter, ReadableLogRecord } from '@opentelemetry/sdk-logs';\nimport {\n  convertLegacyOtlpGrpcOptions,\n  createOtlpGrpcExportDelegate,\n  OTLPGRPCExporterConfigNode,\n} from '@opentelemetry/otlp-grpc-exporter-base';\nimport { ProtobufLogsSerializer } from '@opentelemetry/otlp-transformer';\nimport { OTLPExporterBase } from '@opentelemetry/otlp-exporter-base';\n\n/**\n * OTLP Logs Exporter for Node\n */\nexport class OTLPLogExporter\n  extends OTLPExporterBase<ReadableLogRecord[]>\n  implements LogRecordExporter\n{\n  constructor(config: OTLPGRPCExporterConfigNode = {}) {\n    super(\n      createOtlpGrpcExportDelegate(\n        convertLegacyOtlpGrpcOptions(config, 'LOGS'),\n        ProtobufLogsSerializer,\n        'LogsExportService',\n        '/opentelemetry.proto.collector.logs.v1.LogsService/Export'\n      )\n    );\n  }\n}\n"]}