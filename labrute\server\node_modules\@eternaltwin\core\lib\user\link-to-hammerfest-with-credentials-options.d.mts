import { RecordIoType } from "kryo/record";
import { HammerfestPassword } from "../hammerfest/hammerfest-password.mjs";
import { HammerfestServer } from "../hammerfest/hammerfest-server.mjs";
import { HammerfestUsername } from "../hammerfest/hammerfest-username.mjs";
import { LinkToHammerfestMethod } from "./link-to-hammerfest-method.mjs";
import { UserId } from "./user-id.mjs";
export interface LinkToHammerfestWithCredentialsOptions {
    method: LinkToHammerfestMethod.Credentials;
    /**
     * Id of the Eternaltwin user to link.
     */
    userId: UserId;
    /**
     * Hammerfest server.
     */
    hammerfestServer: HammerfestServer;
    /**
     * Username for the Hammerfest user.
     */
    hammerfestUsername: HammerfestUsername;
    /**
     * Password for the Hammerfest user.
     */
    hammerfestPassword: HammerfestPassword;
}
export declare const $LinkToHammerfestWithCredentialsOptions: RecordIoType<LinkToHammerfestWithCredentialsOptions>;
