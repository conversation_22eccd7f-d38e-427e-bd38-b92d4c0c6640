import { LiteralUnionType } from "kryo/literal-union";
/**
 * A Dinoparc dinoz race.
 */
export type DinoparcDinozRace = "Cargou" | "Castivore" | "Gluon" | "Gorilloz" | "Hippoclamp" | "Kabu<PERSON>" | "Korgon" | "<PERSON><PERSON>" | "<PERSON>ueffe" | "Ouistiti" | "Picori" | "<PERSON><PERSON><PERSON>" | "P<PERSON>z" | "Rokky" | "<PERSON>z" | "<PERSON><PERSON><PERSON>" | "Sir<PERSON>" | "Wanwan" | "Winks";
export declare const $DinoparcDinozRace: LiteralUnionType<DinoparcDinozRace>;
