import { TaggedUnionType } from "kryo/tagged-union";
import { LinkToTwinoidWithOauthOptions } from "./link-to-twinoid-with-oauth-options.mjs";
import { LinkToTwinoidWithRefOptions } from "./link-to-twinoid-with-ref-options.mjs";
export type LinkToTwinoidOptions = LinkToTwinoidWithOauthOptions | LinkToTwinoidWithRefOptions;
export declare const $LinkToTwinoidOptions: TaggedUnionType<LinkToTwinoidOptions>;
