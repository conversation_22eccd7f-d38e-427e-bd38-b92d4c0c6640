import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $OauthCode } from "./oauth-code.mjs";
import { $OauthState } from "./oauth-state.mjs";
export const $OauthAuthorizationResponseOk = new RecordType({
    properties: {
        code: { type: $OauthCode },
        state: { type: $OauthState, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=oauth-authorization-response-ok.mjs.map