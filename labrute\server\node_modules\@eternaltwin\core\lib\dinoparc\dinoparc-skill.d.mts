import { LiteralUnionType } from "kryo/literal-union";
/**
 * A Dinoparc skill.
 */
export type DinoparcSkill = "Bargain" | "Camouflage" | "Climb" | "Cook" | "Counterattack" | "Dexterity" | "Dig" | "EarthApprentice" | "FireApprentice" | "FireProtection" | "Intelligence" | "Juggle" | "Jump" | "Luck" | "MartialArts" | "Medicine" | "Mercenary" | "Music" | "Navigation" | "Perception" | "Provoke" | "Run" | "Saboteur" | "ShadowPower" | "Spy" | "Stamina" | "Steal" | "Strategy" | "Strength" | "Survival" | "Swim" | "ThunderApprentice" | "TotemThief" | "WaterApprentice";
export declare const $DinoparcSkill: LiteralUnionType<DinoparcSkill>;
