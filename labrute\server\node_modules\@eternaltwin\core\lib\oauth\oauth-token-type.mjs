import { CaseStyle } from "kryo";
import { TsEnumType } from "kryo/ts-enum";
export var OauthTokenType;
(function (OauthTokenType) {
    OauthTokenType[OauthTokenType["Bearer"] = 0] = "Bearer";
})(OauthTokenType || (OauthTokenType = {}));
// TODO: Case-insensitive deserialization
export const $OauthTokenType = new TsEnumType({
    enum: OauthTokenType,
    changeCase: CaseStyle.PascalCase,
});
//# sourceMappingURL=oauth-token-type.mjs.map