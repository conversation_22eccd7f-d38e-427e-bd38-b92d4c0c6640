import { TryUnionType } from "kryo/try-union";
import { $OauthClient<PERSON>areKey } from "./oauth-client-bare-key.mjs";
import { $OauthClientId } from "./oauth-client-id.mjs";
import { $OauthClientKey } from "./oauth-client-key.mjs";
export const $OauthClientInputRef = new TryUnionType({ variants: [$OauthClientBareKey, $OauthClientId, $OauthClientKey] });
//# sourceMappingURL=oauth-client-input-ref.mjs.map