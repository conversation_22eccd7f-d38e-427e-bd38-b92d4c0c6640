import { RecordIoType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { FieldShortVersions } from "../core/field-short-versions.mjs";
import { ObjectType } from "../core/object-type.mjs";
import { UserDisplayName } from "./user-display-name.mjs";
import { UserId } from "./user-id.mjs";
/**
 * Represents a reference to an Eternaltwin user, with enough to display it.
 */
export interface ShortUser {
    type: ObjectType.User;
    id: UserId;
    displayName: FieldShortVersions<UserDisplayName>;
}
export declare const $ShortUser: RecordIoType<ShortUser>;
/**
 * A short user that may be null.
 */
export type NullableShortUser = null | ShortUser;
export declare const $NullableShortUser: TryUnionType<NullableShortUser>;
