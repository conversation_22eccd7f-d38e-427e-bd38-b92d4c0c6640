import { RecordIoType } from "kryo/record";
import { FieldVersions } from "../core/field-versions.mjs";
import { NullableDate } from "../core/nullable-date.mjs";
import { ObjectType } from "../core/object-type.mjs";
import { UserDisplayName } from "./user-display-name.mjs";
import { UserId } from "./user-id.mjs";
/**
 * Represents an Eternaltwin simple user (without private data).
 *
 * As a simple user, it only has data directly associated with this user.
 * See `User` for a variant combining data from multiple sources (linked users, forum roles, profile events, etc.).
 */
export interface SimpleUser {
    type: ObjectType.User;
    id: UserId;
    createdAt: Date;
    deletedAt: NullableDate;
    displayName: FieldVersions<UserDisplayName>;
    isAdministrator: boolean;
}
export declare const $SimpleUser: RecordIoType<SimpleUser>;
