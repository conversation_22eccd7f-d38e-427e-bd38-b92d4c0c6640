import { RecordIoType } from "kryo/record";
import { TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
import { UserId } from "./user-id.mjs";
export interface UnlinkFromTwinoidOptions {
    /**
     * Id of the Eternaltwin user to link.
     */
    userId: UserId;
    /**
     * User id for the Twinoid user.
     */
    twinoidUserId: TwinoidUserId;
}
export declare const $UnlinkFromTwinoidOptions: RecordIoType<UnlinkFromTwinoidOptions>;
