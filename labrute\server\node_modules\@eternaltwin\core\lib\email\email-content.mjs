import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $EmailBody } from "./email-body.mjs";
import { $EmailTitle } from "./email-title.mjs";
export const $EmailContent = new RecordType({
    properties: {
        title: { type: $EmailTitle },
        textBody: { type: $EmailBody },
        htmlBody: { type: $EmailBody, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=email-content.mjs.map