import { RecordIoType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { ObjectType } from "../core/object-type.mjs";
import { VersionedEtwinLink } from "../link/versioned-etwin-link.mjs";
import { NullableLatestTemporal } from "../temporal/latest-temporal.mjs";
import { DinoparcCollection } from "./dinoparc-collection.mjs";
import { DinoparcDinozIdRef } from "./dinoparc-dinoz-id-ref.mjs";
import { DinoparcItemCounts } from "./dinoparc-item-counts.mjs";
import { DinoparcServer } from "./dinoparc-server.mjs";
import { DinoparcUserId } from "./dinoparc-user-id.mjs";
import { DinoparcUsername } from "./dinoparc-username.mjs";
export interface EtwinDinoparcUser {
    type: ObjectType.DinoparcUser;
    server: DinoparcServer;
    id: DinoparcUserId;
    archivedAt: Date;
    username: DinoparcUsername;
    coins: NullableLatestTemporal<number>;
    dinoz: NullableLatestTemporal<DinoparcDinozIdRef[]>;
    inventory: NullableLatestTemporal<DinoparcItemCounts>;
    collection: NullableLatestTemporal<DinoparcCollection>;
    etwin: VersionedEtwinLink;
}
export declare const $EtwinDinoparcUser: RecordIoType<EtwinDinoparcUser>;
export type NullableEtwinDinoparcUser = null | EtwinDinoparcUser;
export declare const $NullableEtwinDinoparcUser: TryUnionType<NullableEtwinDinoparcUser>;
