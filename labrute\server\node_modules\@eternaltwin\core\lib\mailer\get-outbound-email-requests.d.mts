import { RecordIoType } from "kryo/record";
import { PeriodLower } from "../core/period-lower.mjs";
import { OutboundEmailId } from "./outbound-email-id.mjs";
export interface GetOutboundEmailRequests {
    /**
     * API time, for time-travel querying
     */
    time?: Date;
    /**
     * Filter email requests created in the provided period
     */
    createdAt?: PeriodLower;
    /**
     * Filter email requests for any of the outbound emails in the set
     */
    outboundEmailId?: Set<OutboundEmailId>;
    /**
     * Maximum number of results to return
     */
    limit: number;
}
export declare const $GetOutboundEmailRequests: RecordIoType<GetOutboundEmailRequests>;
