import { RecordIoType } from "kryo/record";
import { NullableDate } from "../core/nullable-date.mjs";
import { CompleteUser } from "./complete-user.mjs";
export interface EnableUserOut {
    deletedAt: NullableDate;
    current: CompleteUser;
    username: unknown;
    dinoparcCom: unknown;
    enDinoparcCom: unknown;
    hammerfestEs: unknown;
    hammerfestFr: unknown;
    hfestNet: unknown;
    spDinoparcCom: unknown;
    twinoid: unknown;
}
export declare const $EnableUserOut: RecordIoType<EnableUserOut>;
