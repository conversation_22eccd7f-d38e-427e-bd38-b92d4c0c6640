import { RecordIoType } from "kryo/record";
import { <PERSON><PERSON><PERSON><PERSON>assword } from "./dinoparc-password.mjs";
import { DinoparcServer } from "./dinoparc-server.mjs";
import { <PERSON>parcUsername } from "./dinoparc-username.mjs";
export interface DinoparcCredentials {
    server: <PERSON>par<PERSON>Server;
    username: <PERSON><PERSON><PERSON><PERSON>sername;
    password: <PERSON><PERSON><PERSON><PERSON>assword;
}
export declare const $DinoparcCredentials: RecordIoType<DinoparcCredentials>;
