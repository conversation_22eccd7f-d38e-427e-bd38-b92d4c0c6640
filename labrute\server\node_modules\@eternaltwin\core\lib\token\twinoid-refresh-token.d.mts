import { RecordIoType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { RfcOauthRefreshTokenKey } from "../oauth/rfc-oauth-refresh-token-key.mjs";
import { TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
export interface TwinoidRefreshToken {
    key: RfcOauthRefreshTokenKey;
    ctime: Date;
    atime: Date;
    twinoidUserId: TwinoidUserId;
}
export declare const $TwinoidRefreshToken: RecordIoType<TwinoidRefreshToken>;
export type NullableTwinoidRefreshToken = null | TwinoidRefreshToken;
export declare const $NullableTwinoidRefreshToken: TryUnionType<NullableTwinoidRefreshToken>;
