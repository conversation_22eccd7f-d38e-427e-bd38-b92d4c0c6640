import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $UserFieldsType, UserFieldsType } from "./user-fields-type.mjs";
export const $ShortUserFields = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $UserFieldsType, value: UserFieldsType.Short }) },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const SHORT_USER_FIELDS = Object.freeze({ type: UserFieldsType.Short });
//# sourceMappingURL=short-user-fields.mjs.map