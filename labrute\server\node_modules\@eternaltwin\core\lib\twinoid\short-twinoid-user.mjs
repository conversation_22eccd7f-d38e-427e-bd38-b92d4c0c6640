import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $TwinoidUserDisplayName } from "./twinoid-user-display-name.mjs";
import { $TwinoidUserId } from "./twinoid-user-id.mjs";
export const $ShortTwinoidUser = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.TwinoidUser }) },
        id: { type: $TwinoidUserId },
        displayName: { type: $TwinoidUserDisplayName },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableShortTwinoidUser = new TryUnionType({ variants: [$Null, $ShortTwinoidUser] });
//# sourceMappingURL=short-twinoid-user.mjs.map