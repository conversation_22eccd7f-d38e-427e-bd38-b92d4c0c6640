import { CaseStyle } from "kryo";
import { $Uint16 } from "kryo/integer";
import { RecordType } from "kryo/record";
export const $DinoparcDinozElements = new RecordType({
    properties: {
        fire: { type: $Uint16 },
        earth: { type: $Uint16 },
        water: { type: $Uint16 },
        thunder: { type: $Uint16 },
        air: { type: $Uint16 },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=dinoparc-dinoz-elements.mjs.map