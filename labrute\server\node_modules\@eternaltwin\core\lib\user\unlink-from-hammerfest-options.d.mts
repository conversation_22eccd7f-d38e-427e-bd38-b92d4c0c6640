import { RecordIoType } from "kryo/record";
import { HammerfestServer } from "../hammerfest/hammerfest-server.mjs";
import { HammerfestUserId } from "../hammerfest/hammerfest-user-id.mjs";
import { UserId } from "./user-id.mjs";
export interface UnlinkFromHammerfestOptions {
    /**
     * Id of the Eternaltwin user to link.
     */
    userId: UserId;
    /**
     * Hammerfest server.
     */
    hammerfestServer: HammerfestServer;
    /**
     * User id for the Hammerfest user.
     */
    hammerfestUserId: HammerfestUserId;
}
export declare const $UnlinkFromHammerfestOptions: RecordIoType<UnlinkFromHammerfestOptions>;
