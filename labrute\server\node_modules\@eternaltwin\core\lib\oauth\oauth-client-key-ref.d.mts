import { RecordIoType } from "kryo/record";
import { ObjectType } from "../core/object-type.mjs";
import { OauthClientId } from "./oauth-client-id.mjs";
import { OauthClient<PERSON>ey } from "./oauth-client-key.mjs";
export interface OauthClientKeyRef {
    type: ObjectType.OauthClient;
    id?: OauthClientId;
    key: OauthClientKey;
}
export declare const $OauthClientKeyRef: RecordIoType<OauthClientKeyRef>;
