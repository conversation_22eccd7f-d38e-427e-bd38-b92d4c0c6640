import { RecordIoType } from "kryo/record";
import { ObjectType } from "../core/object-type.mjs";
import { DinoparcDinozId } from "./dinoparc-dinoz-id.mjs";
import { DinoparcServer } from "./dinoparc-server.mjs";
/**
 * A reference uniquely identifying a Dinoparc dinoz.
 */
export interface DinoparcDinozIdRef {
    type: ObjectType.DinoparcDinoz;
    server: DinoparcServer;
    id: DinoparcDinozId;
}
export declare const $DinoparcDinozIdRef: RecordIoType<DinoparcDinozIdRef>;
