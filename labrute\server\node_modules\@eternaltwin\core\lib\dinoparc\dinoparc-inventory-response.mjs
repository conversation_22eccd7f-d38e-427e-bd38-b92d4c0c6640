import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $DinoparcItemCounts } from "./dinoparc-item-counts.mjs";
import { $DinoparcSessionUser } from "./dinoparc-session-user.mjs";
export const $DinoparcInventoryResponse = new RecordType({
    properties: {
        sessionUser: { type: $DinoparcSessionUser },
        inventory: { type: $DinoparcItemCounts },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=dinoparc-inventory-response.mjs.map