import { RecordIoType } from "kryo/record";
import { NullablePasswordHash } from "../password/password-hash.mjs";
import { UserDisplayName } from "./user-display-name.mjs";
import { NullableUsername } from "./username.mjs";
export interface UpdateStoreUserPatch {
    displayName?: UserDisplayName;
    username?: NullableUsername;
    password?: NullablePasswordHash;
}
export declare const $UpdateStoreUserPatch: RecordIoType<UpdateStoreUserPatch>;
