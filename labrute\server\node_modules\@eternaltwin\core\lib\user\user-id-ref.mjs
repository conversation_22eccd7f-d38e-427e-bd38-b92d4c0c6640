import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $UserId } from "./user-id.mjs";
export const $UserIdRef = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.User }) },
        id: { type: $UserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableUserIdRef = new TryUnionType({ variants: [$Null, $UserIdRef] });
//# sourceMappingURL=user-id-ref.mjs.map