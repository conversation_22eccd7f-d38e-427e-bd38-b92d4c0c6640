import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $ShortTwinoidSite } from "./short-twinoid-site.mjs";
import { $TwinoidSiteUserId } from "./twinoid-site-user-id.mjs";
export const $TwinoidSiteUser = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.TwinoidSiteUser }) },
        site: { type: $ShortTwinoidSite },
        id: { type: $TwinoidSiteUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=twinoid-site-user.mjs.map