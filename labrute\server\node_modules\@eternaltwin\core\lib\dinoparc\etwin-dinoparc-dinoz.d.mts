import { RecordIoType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { ObjectType } from "../core/object-type.mjs";
import { NullableLatestTemporal } from "../temporal/latest-temporal.mjs";
import { DinoparcDinozElements } from "./dinoparc-dinoz-elements.mjs";
import { DinoparcDinozId } from "./dinoparc-dinoz-id.mjs";
import { NullableDinoparcDinozName } from "./dinoparc-dinoz-name.mjs";
import { DinoparcDinozRace } from "./dinoparc-dinoz-race.mjs";
import { DinoparcLocationId } from "./dinoparc-location-id.mjs";
import { DinoparcServer } from "./dinoparc-server.mjs";
import { DinoparcSkillLevels } from "./dinoparc-skill-levels.mjs";
import { ShortDinoparcUser } from "./short-dinoparc-user.mjs";
export interface EtwinDinoparcDinoz {
    type: ObjectType.DinoparcDinoz;
    server: DinoparcServer;
    id: DinoparcDinozId;
    archivedAt: Date;
    name: NullableLatestTemporal<NullableDinoparcDinozName>;
    owner: NullableLatestTemporal<ShortDinoparcUser>;
    location: NullableLatestTemporal<DinoparcLocationId>;
    race: NullableLatestTemporal<DinoparcDinozRace>;
    skin: NullableLatestTemporal<string>;
    life: NullableLatestTemporal<number>;
    level: NullableLatestTemporal<number>;
    experience: NullableLatestTemporal<number>;
    danger: NullableLatestTemporal<number>;
    inTournament: NullableLatestTemporal<boolean>;
    elements: NullableLatestTemporal<DinoparcDinozElements>;
    skills: NullableLatestTemporal<DinoparcSkillLevels>;
}
export declare const $EtwinDinoparcDinoz: RecordIoType<EtwinDinoparcDinoz>;
export type NullableEtwinDinoparcDinoz = null | EtwinDinoparcDinoz;
export declare const $NullableEtwinDinoparcDinoz: TryUnionType<NullableEtwinDinoparcDinoz>;
