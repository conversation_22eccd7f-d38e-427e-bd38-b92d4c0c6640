import { RecordIoType } from "kryo/record";
import { UserIdRef } from "../user/user-id-ref.mjs";
import { OauthClientIdRef } from "./oauth-client-id-ref.mjs";
import { RfcOauthAccessTokenKey } from "./rfc-oauth-access-token-key.mjs";
export interface StoredOauthAccessToken {
    key: RfcOauthAccessTokenKey;
    ctime: Date;
    atime: Date;
    expirationTime: Date;
    user: UserIdRef;
    client: OauthClientIdRef;
}
export declare const $StoredOauthAccessToken: RecordIoType<StoredOauthAccessToken>;
