import { RecordIoType } from "kryo/record";
import { TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
import { LinkToTwinoidMethod } from "./link-to-twinoid-method.mjs";
import { UserId } from "./user-id.mjs";
export interface LinkToTwinoidWithRefOptions {
    method: LinkToTwinoidMethod.Ref;
    /**
     * Id of the Eternaltwin user to link.
     */
    userId: UserId;
    /**
     * User id for the Twinoid user.
     */
    twinoidUserId: TwinoidUserId;
}
export declare const $LinkToTwinoidWithRefOptions: RecordIoType<LinkToTwinoidWithRefOptions>;
