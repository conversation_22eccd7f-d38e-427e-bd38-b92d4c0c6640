import { RecordIoType } from "kryo/record";
import { OauthAccessToken } from "../oauth/oauth-access-token.mjs";
import { LinkToTwinoidMethod } from "./link-to-twinoid-method.mjs";
import { UserId } from "./user-id.mjs";
export interface LinkToTwinoidWithOauthOptions {
    method: LinkToTwinoidMethod.Oauth;
    /**
     * Id of the Eternaltwin user to link.
     */
    userId: UserId;
    /**
     * Oauth access token.
     */
    accessToken: OauthAccessToken;
}
export declare const $LinkToTwinoidWithOauthOptions: RecordIoType<LinkToTwinoidWithOauthOptions>;
