import { RecordIoType } from "kryo/record";
import { ShortUser } from "../user/short-user.mjs";
import { EtwinOauthAccessTokenKey } from "./etwin-oauth-access-token-key.mjs";
import { ShortOauthClient } from "./short-oauth-client.mjs";
export interface CompleteOauthAccessToken {
    key: EtwinOauthAccessTokenKey;
    ctime: Date;
    atime: Date;
    expirationTime: Date;
    user: ShortUser;
    client: ShortOauthClient;
}
export declare const $CompleteOauthAccessToken: RecordIoType<CompleteOauthAccessToken>;
