import { RecordIoType } from "kryo/record";
import { NullableEmailAddress } from "../email/email-address.mjs";
import { NullablePasswordHash } from "../password/password-hash.mjs";
import { UserDisplayName } from "./user-display-name.mjs";
import { NullableUsername } from "./username.mjs";
export interface CreateUserOptions {
    displayName: UserDisplayName;
    email: NullableEmailAddress;
    username: NullableUsername;
    password: NullablePasswordHash;
}
export declare const $CreateUserOptions: RecordIoType<CreateUserOptions>;
