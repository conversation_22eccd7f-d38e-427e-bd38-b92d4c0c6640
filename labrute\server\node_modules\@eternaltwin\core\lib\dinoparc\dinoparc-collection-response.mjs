import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $DinoparcCollection } from "./dinoparc-collection.mjs";
import { $DinoparcSessionUser } from "./dinoparc-session-user.mjs";
export const $DinoparcCollectionResponse = new RecordType({
    properties: {
        sessionUser: { type: $DinoparcSessionUser },
        collection: { type: $DinoparcCollection },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=dinoparc-collection-response.mjs.map