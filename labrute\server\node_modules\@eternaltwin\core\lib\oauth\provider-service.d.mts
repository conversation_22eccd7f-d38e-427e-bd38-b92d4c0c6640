import { RecordIoType } from "kryo/record";
import { AuthContext } from "../auth/auth-context.mjs";
import { ClockService } from "../clock/service.mjs";
import { UuidGenerator } from "../core/uuid-generator.mjs";
import { UserStore } from "../user/store.mjs";
import { UserIdRef } from "../user/user-id-ref.mjs";
import { CompleteOauthAccessToken } from "./complete-oauth-access-token.mjs";
import { CreateOrUpdateSystemClientOptions } from "./create-or-update-system-client-options.mjs";
import { EtwinOauthAccessTokenRequest } from "./etwin-oauth-access-token-request.mjs";
import { OauthAccessToken } from "./oauth-access-token.mjs";
import { OauthClient } from "./oauth-client.mjs";
import { OauthClientId } from "./oauth-client-id.mjs";
import { OauthClientIdRef } from "./oauth-client-id-ref.mjs";
import { OauthClientInputRef } from "./oauth-client-input-ref.mjs";
import { OauthClientKey } from "./oauth-client-key.mjs";
import { OauthCode } from "./oauth-code.mjs";
import { OauthScopeString } from "./oauth-scope-string.mjs";
import { OauthProviderStore } from "./provider-store.mjs";
import { RfcOauthAccessTokenKey } from "./rfc-oauth-access-token-key.mjs";
export interface OauthProviderService {
    getClientByIdOrKey(_acx: AuthContext, inputRef: OauthClientInputRef): Promise<OauthClient | null>;
    createOrUpdateSystemClient(key: OauthClientKey, options: CreateOrUpdateSystemClientOptions): Promise<OauthClient>;
    createAuthorizationCode(auth: AuthContext, clientId: OauthClientId, scopeString: OauthScopeString | null): Promise<OauthCode>;
    createAccessToken(acx: AuthContext, req: EtwinOauthAccessTokenRequest): Promise<OauthAccessToken>;
    getAndTouchAccessToken(_acx: AuthContext, tokenKey: RfcOauthAccessTokenKey): Promise<{
        token: OauthAccessToken;
        client: OauthClientIdRef;
        user: UserIdRef;
    } | null>;
    getAccessTokenByKey(_acx: AuthContext, atKey: RfcOauthAccessTokenKey): Promise<CompleteOauthAccessToken | null>;
    verifyClientSecret(_acx: AuthContext, clientId: OauthClientId, secret: Uint8Array): Promise<boolean>;
}
export interface DefaultOauthProviderServiceOptions {
    clock: ClockService;
    oauthProviderStore: OauthProviderStore;
    userStore: UserStore;
    tokenSecret: Uint8Array;
    uuidGenerator: UuidGenerator;
}
export declare class DefaultOauthProviderService implements OauthProviderService {
    #private;
    constructor(options: Readonly<DefaultOauthProviderServiceOptions>);
    getClientByIdOrKey(_acx: AuthContext, inputRef: OauthClientInputRef): Promise<OauthClient | null>;
    createOrUpdateSystemClient(key: OauthClientKey, options: CreateOrUpdateSystemClientOptions): Promise<OauthClient>;
    /**
     * From a user authentication, create an authorization code for the provided client.
     */
    createAuthorizationCode(auth: AuthContext, clientId: OauthClientId, scopeString: OauthScopeString | null): Promise<OauthCode>;
    createAccessToken(acx: AuthContext, req: EtwinOauthAccessTokenRequest): Promise<OauthAccessToken>;
    getAndTouchAccessToken(_acx: AuthContext, tokenKey: RfcOauthAccessTokenKey): Promise<{
        token: OauthAccessToken;
        client: OauthClientIdRef;
        user: UserIdRef;
    } | null>;
    getAccessTokenByKey(_acx: AuthContext, atKey: RfcOauthAccessTokenKey): Promise<CompleteOauthAccessToken | null>;
    verifyClientSecret(_acx: AuthContext, clientId: OauthClientId, secret: Uint8Array): Promise<boolean>;
    /**
     * Create the JWT acting as the Oauth authorization code.
     */
    private creatCodeJwt;
    private readCodeJwt;
}
/**
 * Interface describing the content of the JWT acting as the oauth authorization grant code.
 *
 * It is based on the following draft: https://tools.ietf.org/html/draft-bradley-oauth-jwt-encoded-state-00
 */
export interface OauthCodeJwt {
    /**
     * Identifier representing the server granting the access code.
     *
     * It is always the `etwin` string.
     */
    issuer: "etwin";
    /**
     * User id
     */
    subject: string;
    /**
     * The client who was granted the JWT.
     * For external clients, the array only has their id.
     * For system clients, the array contains their id and their key.
     */
    audience: string[];
    scopes: string[];
    issuedAt: number;
    expirationTime: number;
}
export declare const $OauthCodeJwt: RecordIoType<OauthCodeJwt>;
