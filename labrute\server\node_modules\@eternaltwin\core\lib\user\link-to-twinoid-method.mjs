import { TsEnumType } from "kryo/ts-enum";
/**
 * Tag identifying the Twinoid linking method.
 */
export var LinkToTwinoidMethod;
(function (LinkToTwinoidMethod) {
    LinkToTwinoidMethod[LinkToTwinoidMethod["Oauth"] = 0] = "Oauth";
    LinkToTwinoidMethod[LinkToTwinoidMethod["Ref"] = 1] = "Ref";
})(LinkToTwinoidMethod || (LinkToTwinoidMethod = {}));
export const $LinkToTwinoidMethod = new TsEnumType({
    enum: LinkToTwinoidMethod,
});
//# sourceMappingURL=link-to-twinoid-method.mjs.map