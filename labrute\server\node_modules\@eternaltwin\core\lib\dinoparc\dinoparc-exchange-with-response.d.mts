import { RecordIoType } from "kryo/record";
import { DinoparcSessionUser } from "./dinoparc-session-user.mjs";
import { ShortDinoparcDinozWithLevel } from "./short-dinoparc-dinoz-with-level.mjs";
import { ShortDinoparcUser } from "./short-dinoparc-user.mjs";
export interface DinoparcExchangeWithResponse {
    sessionUser: DinoparcSessionUser;
    ownBills: number;
    ownDinoz: ShortDinoparcDinozWithLevel[];
    otherUser: ShortDinoparcUser;
    otherDinoz: ShortDinoparcDinozWithLevel[];
}
export declare const $DinoparcExchangeWithResponse: RecordIoType<DinoparcExchangeWithResponse>;
