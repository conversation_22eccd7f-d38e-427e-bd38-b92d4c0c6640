import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $DinoparcDinoz } from "./dinoparc-dinoz.mjs";
import { $DinoparcSessionUser } from "./dinoparc-session-user.mjs";
export const $DinoparcDinozResponse = new RecordType({
    properties: {
        sessionUser: { type: $DinoparcSessionUser },
        dinoz: { type: $DinoparcDinoz },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=dinoparc-dinoz-response.mjs.map