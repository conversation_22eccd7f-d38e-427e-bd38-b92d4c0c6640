import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $OauthAuthorizationError } from "./oauth-authorization-error.mjs";
import { $OauthState } from "./oauth-state.mjs";
export const $OauthAuthorizationResponseError = new RecordType({
    properties: {
        error: { type: $OauthAuthorizationError },
        state: { type: $OauthState, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=oauth-authorization-response-error.mjs.map