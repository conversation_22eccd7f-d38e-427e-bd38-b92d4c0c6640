import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { SetType } from "kryo/set";
import { $DinoparcEpicRewardKey } from "./dinoparc-epic-reward-key.mjs";
import { $DinoparcRewardId } from "./dinoparc-reward-id.mjs";
export const $DinoparcCollection = new RecordType({
    properties: {
        rewards: { type: new SetType({ itemType: $DinoparcRewardId, maxSize: 49 }) },
        epicRewards: { type: new SetType({ itemType: $DinoparcEpicRewardKey, maxSize: 1000 }) },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=dinoparc-collection.mjs.map