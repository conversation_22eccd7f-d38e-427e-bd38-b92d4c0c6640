import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $HammerfestServer } from "../hammerfest/hammerfest-server.mjs";
import { $HammerfestSessionKey } from "../hammerfest/hammerfest-session-key.mjs";
import { $LinkToHammerfestMethod, LinkToHammerfestMethod } from "./link-to-hammerfest-method.mjs";
import { $UserId } from "./user-id.mjs";
export const $LinkToHammerfestWithSessionKeyOptions = new RecordType({
    properties: {
        method: { type: new LiteralType({ type: $LinkToHammerfestMethod, value: LinkToHammerfestMethod.SessionKey }) },
        userId: { type: $UserId },
        hammerfestServer: { type: $HammerfestServer },
        hammerfestSessionKey: { type: $HammerfestSessionKey },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=link-to-hammerfest-with-session-key-options.mjs.map