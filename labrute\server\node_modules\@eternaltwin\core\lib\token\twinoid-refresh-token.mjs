import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $RfcOauthRefreshTokenKey } from "../oauth/rfc-oauth-refresh-token-key.mjs";
import { $TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
export const $TwinoidRefreshToken = new RecordType({
    properties: {
        key: { type: $RfcOauthRefreshTokenKey },
        ctime: { type: $Date },
        atime: { type: $Date },
        twinoidUserId: { type: $TwinoidUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableTwinoidRefreshToken = new TryUnionType({ variants: [$Null, $TwinoidRefreshToken] });
//# sourceMappingURL=twinoid-refresh-token.mjs.map