import { RecordIoType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { DinoparcSessionKey } from "./dinoparc-session-key.mjs";
import { ShortDinoparcUser } from "./short-dinoparc-user.mjs";
export interface DinoparcSession {
    ctime: Date;
    atime: Date;
    key: DinoparcSessionKey;
    user: ShortDinoparcUser;
}
export declare const $DinoparcSession: RecordIoType<DinoparcSession>;
export type NullableDinoparcSession = null | DinoparcSession;
export declare const $NullableDinoparcSession: TryUnionType<NullableDinoparcSession>;
