import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { $Uint8 } from "kryo/integer";
import { RecordType } from "kryo/record";
import { $PeriodLower } from "../core/period-lower.mjs";
export const $GetOutboundEmails = new RecordType({
    properties: {
        time: { type: $Date, optional: true },
        createdAt: { type: $PeriodLower, optional: true },
        limit: { type: $Uint8 },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=get-outbound-emails.mjs.map