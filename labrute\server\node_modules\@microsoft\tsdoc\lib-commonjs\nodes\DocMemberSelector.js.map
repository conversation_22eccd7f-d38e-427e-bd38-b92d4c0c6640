{"version": 3, "file": "DocMemberSelector.js", "sourceRoot": "", "sources": ["../../src/nodes/DocMemberSelector.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,qCAA+F;AAC/F,uDAAsD;AAEtD,2CAAuD;AAEvD;;GAEG;AACH,IAAY,YA8BX;AA9BD,WAAY,YAAY;IACtB;;;OAGG;IACH,+BAAe,CAAA;IAEf;;OAEG;IACH,iCAAiB,CAAA;IAEjB;;;;;;;;;OASG;IACH,+BAAe,CAAA;IAEf;;;;OAIG;IACH,+BAAe,CAAA;AACjB,CAAC,EA9BW,YAAY,GAAZ,oBAAY,KAAZ,oBAAY,QA8BvB;AAgBD;GACG;AACH;IAAuC,qCAAO;IAkB5C;;;OAGG;IACH,2BAAmB,UAA6E;QAAhG,YACE,kBAAM,UAAU,CAAC,SAoDlB;QAlDC,IAAI,iBAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE;YAC1C,KAAI,CAAC,gBAAgB,GAAG,IAAI,uBAAU,CAAC;gBACrC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,cAAc;gBACvC,OAAO,EAAE,UAAU,CAAC,eAAe;aACpC,CAAC,CAAC;YAEH,KAAI,CAAC,SAAS,GAAG,UAAU,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;SACxD;aAAM;YACL,KAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC;SACtC;QAED,KAAI,CAAC,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC;QACxC,KAAI,CAAC,aAAa,GAAG,SAAS,CAAC;QAE/B,gGAAgG;QAEhG,IAAI,KAAI,CAAC,SAAS,CAAC,MAAM,KAAK,CAAC,EAAE;YAC/B,KAAI,CAAC,aAAa,GAAG,wCAAwC,CAAC;SAC/D;aAAM,IAAI,iBAAiB,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE;YAC1E,kCAAkC;YAElC,IAAI,iBAAiB,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE;gBAC/D,KAAI,CAAC,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC;aACzC;iBAAM;gBACL,KAAI,CAAC,aAAa,GAAG,2EAA2E,CAAC;aAClG;SACF;aAAM,IAAI,iBAAiB,CAAC,wBAAwB,CAAC,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE;YAC1E,iCAAiC;YAEjC,IAAI,iBAAiB,CAAC,oBAAoB,CAAC,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE;gBAC/D,KAAI,CAAC,aAAa,GAAG,YAAY,CAAC,KAAK,CAAC;aACzC;iBAAM;gBACL,KAAI,CAAC,aAAa;oBAChB,oEAAoE;wBACpE,mDAAmD,CAAC;aACvD;SACF;aAAM;YACL,IAAI,2BAAY,CAAC,gBAAgB,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE;gBACjD,KAAI,CAAC,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC;aAC1C;iBAAM,IAAI,iBAAiB,CAAC,yBAAyB,CAAC,IAAI,CAAC,KAAI,CAAC,SAAS,CAAC,EAAE;gBAC3E,8CAA8C;gBAC9C,KAAI,CAAC,aAAa;oBAChB,kBAAgB,IAAI,CAAC,SAAS,CAAC,KAAI,CAAC,SAAS,CAAG;wBAChD,iDAAiD,CAAC;aACrD;iBAAM;gBACL,6CAA6C;gBAC7C,KAAI,CAAC,aAAa,GAAG,6BAA6B,CAAC;aACpD;SACF;;IACH,CAAC;IAGD,sBAAW,mCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,cAAc,CAAC;QACpC,CAAC;;;OAAA;IAUD,sBAAW,uCAAQ;QARnB;;;;;;;WAOG;aACH;YACE,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAKD,sBAAW,2CAAY;QAHvB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;;;OAAA;IAMD,sBAAW,2CAAY;QAJvB;;;WAGG;aACH;YACE,OAAO,IAAI,CAAC,aAAa,CAAC;QAC5B,CAAC;;;OAAA;IAED,gBAAgB;IACN,2CAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;IACjC,CAAC;IA/GuB,0CAAwB,GAAW,QAAQ,CAAC;IAE5C,sCAAoB,GAAW,eAAe,CAAC;IAE/C,0CAAwB,GAAW,UAAU,CAAC;IAE9C,sCAAoB,GAAW,oBAAoB,CAAC;IAEpD,2CAAyB,GAAW,WAAW,CAAC;IAwG1E,wBAAC;CAAA,AAjHD,CAAuC,iBAAO,GAiH7C;AAjHY,8CAAiB", "sourcesContent": ["import { DocNode<PERSON>ind, DocNode, IDocNodeParsedParameters, IDocNodeParameters } from './DocNode';\r\nimport { StringChecks } from '../parser/StringChecks';\r\nimport { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Kinds of TSDoc selectors.\r\n */\r\nexport enum SelectorKind {\r\n  /**\r\n   * Used in cases where the parser encounters a string that is incorrect but\r\n   * valid enough that a DocMemberSelector node was created.\r\n   */\r\n  Error = 'error',\r\n\r\n  /**\r\n   * System selectors are always all lower-case and belong to a set of predefined label names.\r\n   */\r\n  System = 'system',\r\n\r\n  /**\r\n   * Index selectors are integer numbers.  They provide an alternative way of referencing\r\n   * overloaded functions, based on the order in which the declarations appear in\r\n   * a source file.\r\n   *\r\n   * @remarks\r\n   * Warning:  Index selectors are not recommended; they are intended to provide a temporary\r\n   * workaround for situations where an external library neglected to declare a `{@label}` tag\r\n   * and cannot be easily fixed.\r\n   */\r\n  Index = 'index',\r\n\r\n  /**\r\n   * Label selectors refer to labels created using the `{@label}` TSDoc tag.\r\n   * The labels are always comprised of upper-case letters or numbers separated by underscores,\r\n   * and the first character cannot be a number.\r\n   */\r\n  Label = 'label'\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocMemberSelector}.\r\n */\r\nexport interface IDocMemberSelectorParameters extends IDocNodeParameters {\r\n  selector: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocMemberSelector}.\r\n */\r\nexport interface IDocMemberSelectorParsedParameters extends IDocNodeParsedParameters {\r\n  selectorExcerpt: TokenSequence;\r\n}\r\n\r\n/**\r\n */\r\nexport class DocMemberSelector extends DocNode {\r\n  private static readonly _likeIndexSelectorRegExp: RegExp = /^[0-9]/;\r\n\r\n  private static readonly _indexSelectorRegExp: RegExp = /^[1-9][0-9]*$/;\r\n\r\n  private static readonly _likeLabelSelectorRegExp: RegExp = /^[A-Z_]/u;\r\n\r\n  private static readonly _labelSelectorRegExp: RegExp = /^[A-Z_][A-Z0-9_]+$/;\r\n\r\n  private static readonly _likeSystemSelectorRegExp: RegExp = /^[a-z]+$/u;\r\n\r\n  private readonly _selector: string;\r\n  private _selectorExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _selectorKind: SelectorKind;\r\n\r\n  private readonly _errorMessage: string | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocMemberSelectorParameters | IDocMemberSelectorParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._selectorExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.MemberSelector,\r\n        content: parameters.selectorExcerpt\r\n      });\r\n\r\n      this._selector = parameters.selectorExcerpt.toString();\r\n    } else {\r\n      this._selector = parameters.selector;\r\n    }\r\n\r\n    this._selectorKind = SelectorKind.Error;\r\n    this._errorMessage = undefined;\r\n\r\n    // The logic below will always either (1) assign selectorKind or (2) else assign an errorMessage\r\n\r\n    if (this._selector.length === 0) {\r\n      this._errorMessage = 'The selector cannot be an empty string';\r\n    } else if (DocMemberSelector._likeIndexSelectorRegExp.test(this._selector)) {\r\n      // It looks like an index selector\r\n\r\n      if (DocMemberSelector._indexSelectorRegExp.test(this._selector)) {\r\n        this._selectorKind = SelectorKind.Index;\r\n      } else {\r\n        this._errorMessage = 'If the selector begins with a number, it must be a positive integer value';\r\n      }\r\n    } else if (DocMemberSelector._likeLabelSelectorRegExp.test(this._selector)) {\r\n      // It looks like a label selector\r\n\r\n      if (DocMemberSelector._labelSelectorRegExp.test(this._selector)) {\r\n        this._selectorKind = SelectorKind.Label;\r\n      } else {\r\n        this._errorMessage =\r\n          'A label selector must be comprised of upper case letters, numbers,' +\r\n          ' and underscores and must not start with a number';\r\n      }\r\n    } else {\r\n      if (StringChecks.isSystemSelector(this._selector)) {\r\n        this._selectorKind = SelectorKind.System;\r\n      } else if (DocMemberSelector._likeSystemSelectorRegExp.test(this._selector)) {\r\n        // It looks like a system selector, but is not\r\n        this._errorMessage =\r\n          `The selector ${JSON.stringify(this._selector)}` +\r\n          ` is not a recognized TSDoc system selector name`;\r\n      } else {\r\n        // It doesn't look like anything we recognize\r\n        this._errorMessage = 'Invalid syntax for selector';\r\n      }\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.MemberSelector;\r\n  }\r\n\r\n  /**\r\n   * The text representation of the selector.\r\n   *\r\n   * @remarks\r\n   * For system selectors, it will be a predefined lower case name.\r\n   * For label selectors, it will be an upper case name defined using the `{@label}` tag.\r\n   * For index selectors, it will be a positive integer.\r\n   */\r\n  public get selector(): string {\r\n    return this._selector;\r\n  }\r\n\r\n  /**\r\n   * Indicates the kind of selector.\r\n   */\r\n  public get selectorKind(): SelectorKind {\r\n    return this._selectorKind;\r\n  }\r\n\r\n  /**\r\n   * If the `selectorKind` is `SelectorKind.Error`, this string will be defined and provide\r\n   * more detail about why the string was not valid.\r\n   */\r\n  public get errorMessage(): string | undefined {\r\n    return this._errorMessage;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this._selectorExcerpt];\r\n  }\r\n}\r\n"]}