import { TaggedUnionType } from "kryo/tagged-union";
import { LinkToHammerfestWithCredentialsOptions } from "./link-to-hammerfest-with-credentials-options.mjs";
import { LinkToHammerfestWithRefOptions } from "./link-to-hammerfest-with-ref-options.mjs";
import { LinkToHammerfestWithSessionKeyOptions } from "./link-to-hammerfest-with-session-key-options.mjs";
export type LinkToHammerfestOptions = LinkToHammerfestWithCredentialsOptions | LinkToHammerfestWithRefOptions | LinkToHammerfestWithSessionKeyOptions;
export declare const $LinkToHammerfestOptions: TaggedUnionType<LinkToHammerfestOptions>;
