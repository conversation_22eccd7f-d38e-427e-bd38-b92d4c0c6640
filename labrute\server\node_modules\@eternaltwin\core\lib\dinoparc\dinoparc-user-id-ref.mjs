import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $DinoparcServer } from "./dinoparc-server.mjs";
import { $DinoparcUserId } from "./dinoparc-user-id.mjs";
export const $DinoparcUserIdRef = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.DinoparcUser }) },
        server: { type: $DinoparcServer },
        id: { type: $DinoparcUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=dinoparc-user-id-ref.mjs.map