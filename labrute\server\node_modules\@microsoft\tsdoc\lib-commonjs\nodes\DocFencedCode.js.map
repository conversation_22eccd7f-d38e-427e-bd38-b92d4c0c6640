{"version": 3, "file": "DocFencedCode.js", "sourceRoot": "", "sources": ["../../src/nodes/DocFencedCode.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,qCAA+F;AAE/F,2CAAuD;AA4BvD;;;;GAIG;AACH;IAAmC,iCAAO;IAuBxC;;;OAGG;IACH,uBAAmB,UAAqE;QAAxF,YACE,kBAAM,UAAU,CAAC,SA4DlB;QA1DC,IAAI,iBAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE;YAC1C,KAAI,CAAC,oBAAoB,GAAG,IAAI,uBAAU,CAAC;gBACzC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,uBAAuB;gBAChD,OAAO,EAAE,UAAU,CAAC,mBAAmB;aACxC,CAAC,CAAC;YACH,IAAI,UAAU,CAAC,+BAA+B,EAAE;gBAC9C,KAAI,CAAC,gCAAgC,GAAG,IAAI,uBAAU,CAAC;oBACrD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,wBAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,+BAA+B;iBACpD,CAAC,CAAC;aACJ;YAED,IAAI,UAAU,CAAC,eAAe,EAAE;gBAC9B,KAAI,CAAC,gBAAgB,GAAG,IAAI,uBAAU,CAAC;oBACrC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,wBAAW,CAAC,mBAAmB;oBAC5C,OAAO,EAAE,UAAU,CAAC,eAAe;iBACpC,CAAC,CAAC;aACJ;YACD,IAAI,UAAU,CAAC,2BAA2B,EAAE;gBAC1C,KAAI,CAAC,4BAA4B,GAAG,IAAI,uBAAU,CAAC;oBACjD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,wBAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,2BAA2B;iBAChD,CAAC,CAAC;aACJ;YAED,KAAI,CAAC,YAAY,GAAG,IAAI,uBAAU,CAAC;gBACjC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,eAAe;gBACxC,OAAO,EAAE,UAAU,CAAC,WAAW;aAChC,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,gCAAgC,EAAE;gBAC/C,KAAI,CAAC,iCAAiC,GAAG,IAAI,uBAAU,CAAC;oBACtD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,wBAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,gCAAgC;iBACrD,CAAC,CAAC;aACJ;YACD,KAAI,CAAC,oBAAoB,GAAG,IAAI,uBAAU,CAAC;gBACzC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,uBAAuB;gBAChD,OAAO,EAAE,UAAU,CAAC,mBAAmB;aACxC,CAAC,CAAC;YACH,IAAI,UAAU,CAAC,+BAA+B,EAAE;gBAC9C,KAAI,CAAC,gCAAgC,GAAG,IAAI,uBAAU,CAAC;oBACrD,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,wBAAW,CAAC,OAAO;oBAChC,OAAO,EAAE,UAAU,CAAC,+BAA+B;iBACpD,CAAC,CAAC;aACJ;SACF;aAAM;YACL,KAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC;YAC7B,KAAI,CAAC,SAAS,GAAG,UAAU,CAAC,QAAQ,CAAC;SACtC;;IACH,CAAC;IAGD,sBAAW,+BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,UAAU,CAAC;QAChC,CAAC;;;OAAA;IAiBD,sBAAW,mCAAQ;QAfnB;;;;;;;;;;;;;;WAcG;aACH;YACE,IAAI,IAAI,CAAC,SAAS,KAAK,SAAS,EAAE;gBAChC,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS,EAAE;oBACvC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;iBAC3D;qBAAM;oBACL,IAAI,CAAC,SAAS,GAAG,EAAE,CAAC;iBACrB;aACF;YACD,OAAO,IAAI,CAAC,SAAS,CAAC;QACxB,CAAC;;;OAAA;IAKD,sBAAW,+BAAI;QAHf;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,IAAI,CAAC,YAAY,KAAK,SAAS,EAAE;oBACnC,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;iBACnD;aACF;YACD,OAAO,IAAI,CAAC,KAAM,CAAC;QACrB,CAAC;;;OAAA;IAED,gBAAgB;IACN,uCAAe,GAAzB;QACE,OAAO;YACL,IAAI,CAAC,oBAAoB;YACzB,IAAI,CAAC,gCAAgC;YAErC,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,4BAA4B;YAEjC,IAAI,CAAC,YAAY;YAEjB,IAAI,CAAC,iCAAiC;YACtC,IAAI,CAAC,oBAAoB;YACzB,IAAI,CAAC,gCAAgC;SACtC,CAAC;IACJ,CAAC;IACH,oBAAC;AAAD,CAAC,AArJD,CAAmC,iBAAO,GAqJzC;AArJY,sCAAa", "sourcesContent": ["import { DocNodeKind, IDocNodeParameters, DocNode, IDocNodeParsedParameters } from './DocNode';\r\nimport { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocFencedCode}.\r\n */\r\nexport interface IDocFencedCodeParameters extends IDocNodeParameters {\r\n  language: string;\r\n\r\n  code: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocFencedCode}.\r\n */\r\nexport interface IDocFencedCodeParsedParameters extends IDocNodeParsedParameters {\r\n  openingFenceExcerpt: TokenSequence;\r\n  spacingAfterOpeningFenceExcerpt?: TokenSequence;\r\n\r\n  languageExcerpt?: TokenSequence;\r\n  spacingAfterLanguageExcerpt?: TokenSequence;\r\n\r\n  codeExcerpt: TokenSequence;\r\n\r\n  spacingBeforeClosingFenceExcerpt?: TokenSequence;\r\n  closingFenceExcerpt: TokenSequence;\r\n  spacingAfterClosingFenceExcerpt?: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents CommonMark-style code fence, i.e. a block of program code that\r\n * starts and ends with a line comprised of three backticks.  The opening delimiter\r\n * can also specify a language for a syntax highlighter.\r\n */\r\nexport class DocFencedCode extends DocNode {\r\n  // The opening ``` delimiter and padding\r\n  private readonly _openingFenceExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _spacingAfterOpeningFenceExcerpt: DocExcerpt | undefined;\r\n\r\n  // The optional language string\r\n  private _language: string | undefined;\r\n  private readonly _languageExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _spacingAfterLanguageExcerpt: DocExcerpt | undefined;\r\n\r\n  // The code content\r\n  private _code: string | undefined;\r\n  private readonly _codeExcerpt: DocExcerpt | undefined;\r\n\r\n  // The closing ``` delimiter and padding\r\n  private readonly _spacingBeforeClosingFenceExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _closingFenceExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _spacingAfterClosingFenceExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocFencedCodeParameters | IDocFencedCodeParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._openingFenceExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.FencedCode_OpeningFence,\r\n        content: parameters.openingFenceExcerpt\r\n      });\r\n      if (parameters.spacingAfterOpeningFenceExcerpt) {\r\n        this._spacingAfterOpeningFenceExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterOpeningFenceExcerpt\r\n        });\r\n      }\r\n\r\n      if (parameters.languageExcerpt) {\r\n        this._languageExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.FencedCode_Language,\r\n          content: parameters.languageExcerpt\r\n        });\r\n      }\r\n      if (parameters.spacingAfterLanguageExcerpt) {\r\n        this._spacingAfterLanguageExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterLanguageExcerpt\r\n        });\r\n      }\r\n\r\n      this._codeExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.FencedCode_Code,\r\n        content: parameters.codeExcerpt\r\n      });\r\n\r\n      if (parameters.spacingBeforeClosingFenceExcerpt) {\r\n        this._spacingBeforeClosingFenceExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingBeforeClosingFenceExcerpt\r\n        });\r\n      }\r\n      this._closingFenceExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.FencedCode_ClosingFence,\r\n        content: parameters.closingFenceExcerpt\r\n      });\r\n      if (parameters.spacingAfterClosingFenceExcerpt) {\r\n        this._spacingAfterClosingFenceExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.Spacing,\r\n          content: parameters.spacingAfterClosingFenceExcerpt\r\n        });\r\n      }\r\n    } else {\r\n      this._code = parameters.code;\r\n      this._language = parameters.language;\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.FencedCode;\r\n  }\r\n\r\n  /**\r\n   * A name that can optionally be included after the opening code fence delimiter,\r\n   * on the same line as the three backticks.  This name indicates the programming language\r\n   * for the code, which a syntax highlighter may use to style the code block.\r\n   *\r\n   * @remarks\r\n   * The TSDoc standard requires that the language \"ts\" should be interpreted to mean TypeScript.\r\n   * Other languages names may be supported, but this is implementation dependent.\r\n   *\r\n   * CommonMark refers to this field as the \"info string\".\r\n   *\r\n   * @privateRemarks\r\n   * Examples of language strings supported by GitHub flavored markdown:\r\n   * https://raw.githubusercontent.com/github/linguist/master/lib/linguist/languages.yml\r\n   */\r\n  public get language(): string | 'ts' | '' {\r\n    if (this._language === undefined) {\r\n      if (this._languageExcerpt !== undefined) {\r\n        this._language = this._languageExcerpt.content.toString();\r\n      } else {\r\n        this._language = '';\r\n      }\r\n    }\r\n    return this._language;\r\n  }\r\n\r\n  /**\r\n   * The text that should be rendered as code.\r\n   */\r\n  public get code(): string {\r\n    if (this._code === undefined) {\r\n      if (this._codeExcerpt !== undefined) {\r\n        this._code = this._codeExcerpt.content.toString();\r\n      }\r\n    }\r\n    return this._code!;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [\r\n      this._openingFenceExcerpt,\r\n      this._spacingAfterOpeningFenceExcerpt,\r\n\r\n      this._languageExcerpt,\r\n      this._spacingAfterLanguageExcerpt,\r\n\r\n      this._codeExcerpt,\r\n\r\n      this._spacingBeforeClosingFenceExcerpt,\r\n      this._closingFenceExcerpt,\r\n      this._spacingAfterClosingFenceExcerpt\r\n    ];\r\n  }\r\n}\r\n"]}