import { CaseStyle } from "kryo";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $UserDot } from "../core/user-dot.mjs";
import { $ShortTwinoidUser } from "../twinoid/short-twinoid-user.mjs";
export const $TwinoidLink = new RecordType({
    properties: {
        link: { type: $UserDot },
        unlink: { type: $Null },
        user: { type: $ShortTwinoidUser },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableTwinoidLink = new TryUnionType({ variants: [$Null, $TwinoidLink] });
//# sourceMappingURL=twinoid-link.mjs.map