import { RecordIoType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { ObjectType } from "../core/object-type.mjs";
import { TwinoidSiteUserId } from "./twinoid-site-user-id.mjs";
export interface TwinoidLinkUser {
    type: ObjectType.TwinoidSiteUser;
    id: TwinoidSiteUserId;
}
export declare const $TwinoidLinkUser: RecordIoType<TwinoidLinkUser>;
export type NullableTwinoidLinkUser = null | TwinoidLinkUser;
export declare const $NullableTwinoidLinkUser: TryUnionType<NullableTwinoidLinkUser>;
