import { RecordIoType } from "kryo/record";
import { Url } from "../core/url.mjs";
import { NullableUserIdRef } from "../user/user-id-ref.mjs";
import { ShortOauthClient } from "./short-oauth-client.mjs";
export interface SimpleOauthClient extends ShortOauthClient {
    appUri: Url;
    callbackUri: Url;
    owner: NullableUserIdRef;
}
export declare const $SimpleOauthClient: RecordIoType<SimpleOauthClient>;
