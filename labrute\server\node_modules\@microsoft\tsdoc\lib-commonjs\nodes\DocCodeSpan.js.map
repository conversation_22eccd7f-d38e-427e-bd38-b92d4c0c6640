{"version": 3, "file": "DocCodeSpan.js", "sourceRoot": "", "sources": ["../../src/nodes/DocCodeSpan.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,qCAA+F;AAE/F,2CAAuD;AAoBvD;;;GAGG;AACH;IAAiC,+BAAO;IAWtC;;;OAGG;IACH,qBAAmB,UAAiE;QAApF,YACE,kBAAM,UAAU,CAAC,SAqBlB;QAnBC,IAAI,iBAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE;YAC1C,KAAI,CAAC,wBAAwB,GAAG,IAAI,uBAAU,CAAC;gBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,yBAAyB;gBAClD,OAAO,EAAE,UAAU,CAAC,uBAAuB;aAC5C,CAAC,CAAC;YACH,KAAI,CAAC,YAAY,GAAG,IAAI,uBAAU,CAAC;gBACjC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,aAAa;gBACtC,OAAO,EAAE,UAAU,CAAC,WAAW;aAChC,CAAC,CAAC;YACH,KAAI,CAAC,wBAAwB,GAAG,IAAI,uBAAU,CAAC;gBAC7C,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,yBAAyB;gBAClD,OAAO,EAAE,UAAU,CAAC,uBAAuB;aAC5C,CAAC,CAAC;SACJ;aAAM;YACL,KAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC;SAC9B;;IACH,CAAC;IAGD,sBAAW,6BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,QAAQ,CAAC;QAC9B,CAAC;;;OAAA;IAKD,sBAAW,6BAAI;QAHf;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;aACpD;YACD,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;;;OAAA;IAED,gBAAgB;IACN,qCAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,wBAAwB,CAAC,CAAC;IAC3F,CAAC;IACH,kBAAC;AAAD,CAAC,AA1DD,CAAiC,iBAAO,GA0DvC;AA1DY,kCAAW", "sourcesContent": ["import { DocNodeKind, IDocNodeParameters, DocNode, IDocNodeParsedParameters } from './DocNode';\r\nimport { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocCodeSpan}.\r\n */\r\nexport interface IDocCodeSpanParameters extends IDocNodeParameters {\r\n  code: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocCodeSpan}.\r\n */\r\nexport interface IDocCodeSpanParsedParameters extends IDocNodeParsedParameters {\r\n  openingDelimiterExcerpt: TokenSequence;\r\n\r\n  codeExcerpt: TokenSequence;\r\n\r\n  closingDelimiterExcerpt: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents CommonMark-style code span, i.e. code surrounded by\r\n * backtick characters.\r\n */\r\nexport class DocCodeSpan extends DocNode {\r\n  // The opening ` delimiter\r\n  private readonly _openingDelimiterExcerpt: DocExcerpt | undefined;\r\n\r\n  // The code content\r\n  private _code: string | undefined;\r\n  private readonly _codeExcerpt: DocExcerpt | undefined;\r\n\r\n  // The closing ` delimiter\r\n  private readonly _closingDelimiterExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocCodeSpanParameters | IDocCodeSpanParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._openingDelimiterExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.CodeSpan_OpeningDelimiter,\r\n        content: parameters.openingDelimiterExcerpt\r\n      });\r\n      this._codeExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.CodeSpan_Code,\r\n        content: parameters.codeExcerpt\r\n      });\r\n      this._closingDelimiterExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.CodeSpan_ClosingDelimiter,\r\n        content: parameters.closingDelimiterExcerpt\r\n      });\r\n    } else {\r\n      this._code = parameters.code;\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.CodeSpan;\r\n  }\r\n\r\n  /**\r\n   * The text that should be rendered as code, excluding the backtick delimiters.\r\n   */\r\n  public get code(): string {\r\n    if (this._code === undefined) {\r\n      this._code = this._codeExcerpt!.content.toString();\r\n    }\r\n    return this._code;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this._openingDelimiterExcerpt, this._codeExcerpt, this._closingDelimiterExcerpt];\r\n  }\r\n}\r\n"]}