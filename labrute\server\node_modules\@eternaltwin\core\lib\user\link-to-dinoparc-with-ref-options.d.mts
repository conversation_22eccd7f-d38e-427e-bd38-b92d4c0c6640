import { RecordIoType } from "kryo/record";
import { DinoparcServer } from "../dinoparc/dinoparc-server.mjs";
import { DinoparcUserId } from "../dinoparc/dinoparc-user-id.mjs";
import { LinkToDinoparcMethod } from "./link-to-dinoparc-method.mjs";
import { UserId } from "./user-id.mjs";
export interface LinkToDinoparcWithRefOptions {
    method: LinkToDinoparcMethod.Ref;
    /**
     * Id of the Eternaltwin user to link.
     */
    userId: UserId;
    /**
     * Dinoparc server.
     */
    dinoparcServer: DinoparcServer;
    /**
     * User id for the Dinoparc user.
     */
    dinoparcUserId: DinoparcUserId;
}
export declare const $LinkToDinoparcWithRefOptions: RecordIoType<LinkToDinoparcWithRefOptions>;
