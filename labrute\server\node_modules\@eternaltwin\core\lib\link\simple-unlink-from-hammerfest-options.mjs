import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $HammerfestServer } from "../hammerfest/hammerfest-server.mjs";
import { $HammerfestUserId } from "../hammerfest/hammerfest-user-id.mjs";
import { $UserId } from "../user/user-id.mjs";
export const $SimpleUnlinkFromHammerfestOptions = new RecordType({
    properties: {
        userId: { type: $UserId },
        hammerfestServer: { type: $HammerfestServer },
        hammerfestUserId: { type: $HammerfestUserId },
        unlinkedBy: { type: $UserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=simple-unlink-from-hammerfest-options.mjs.map