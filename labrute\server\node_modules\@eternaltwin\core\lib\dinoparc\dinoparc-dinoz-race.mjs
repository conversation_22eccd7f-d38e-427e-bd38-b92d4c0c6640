import { LiteralUnionType } from "kryo/literal-union";
import { $Ucs2String } from "kryo/ucs2-string";
export const $DinoparcDinozRace = new LiteralUnionType({
    type: $Ucs2String,
    values: [
        "<PERSON>gou",
        "Castivore",
        "<PERSON><PERSON><PERSON>",
        "<PERSON>rilloz",
        "Hippoclamp",
        "<PERSON>buki",
        "Korgon",
        "Ku<PERSON>",
        "Moueffe",
        "Ouistiti",
        "Picori",
        "Pigmo<PERSON>",
        "P<PERSON><PERSON>",
        "<PERSON>ok<PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
        "<PERSON><PERSON>",
    ]
});
//# sourceMappingURL=dinoparc-dinoz-race.mjs.map