import { RecordIoType } from "kryo/record";
import { NullablePassword } from "../password/password.mjs";
import { UserDisplayName } from "./user-display-name.mjs";
import { NullableUsername } from "./username.mjs";
export interface UpdateUserPatch {
    displayName?: UserDisplayName;
    username?: NullableUsername;
    password?: NullablePassword;
}
export declare const $UpdateUserPatch: RecordIoType<UpdateUserPatch>;
