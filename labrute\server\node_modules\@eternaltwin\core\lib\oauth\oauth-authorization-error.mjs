import { CaseStyle } from "kryo";
import { TsEnumType } from "kryo/ts-enum";
export var OauthAuthorizationError;
(function (OauthAuthorizationError) {
    /**
     * > The request is missing a required parameter, includes an
     * > invalid parameter value, includes a parameter more than
     * > once, or is otherwise malformed.
     */
    OauthAuthorizationError[OauthAuthorizationError["InvalidRequest"] = 0] = "InvalidRequest";
    /**
     * > The client is not authorized to request an authorization
     * > code using this method.
     */
    OauthAuthorizationError[OauthAuthorizationError["UnauthorizedClient"] = 1] = "UnauthorizedClient";
    /**
     * > The resource owner or authorization server denied the
     * > request.
     */
    OauthAuthorizationError[OauthAuthorizationError["AccessDenied"] = 2] = "AccessDenied";
    /**
     * > The authorization server does not support obtaining an
     * > authorization code using this method.
     */
    OauthAuthorizationError[OauthAuthorizationError["UnsupportedResponseType"] = 3] = "UnsupportedResponseType";
    /**
     * > The requested scope is invalid, unknown, or malformed.
     */
    OauthAuthorizationError[OauthAuthorizationError["InvalidScope"] = 4] = "InvalidScope";
    /**
     * > The authorization server encountered an unexpected
     * > condition that prevented it from fulfilling the request.
     * > (This error code is needed because a 500 Internal Server
     * > Error HTTP status code cannot be returned to the client
     * > via an HTTP redirect.)
     */
    OauthAuthorizationError[OauthAuthorizationError["ScopeError"] = 5] = "ScopeError";
    /**
     * > The authorization server is currently unable to handle
     * > the request due to a temporary overloading or maintenance
     * > of the server.  (This error code is needed because a 503
     * > Service Unavailable HTTP status code cannot be returned
     * > to the client via an HTTP redirect.)
     */
    OauthAuthorizationError[OauthAuthorizationError["TemporarilyUnavailable"] = 6] = "TemporarilyUnavailable";
})(OauthAuthorizationError || (OauthAuthorizationError = {}));
export const $OauthAuthorizationError = new TsEnumType({
    enum: OauthAuthorizationError,
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=oauth-authorization-error.mjs.map