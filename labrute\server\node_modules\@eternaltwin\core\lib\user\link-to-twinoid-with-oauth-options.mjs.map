{"version": 3, "file": "link-to-twinoid-with-oauth-options.mjs", "sourceRoot": "", "sources": ["../../src/lib/user/link-to-twinoid-with-oauth-options.mts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,EAAE,WAAW,EAAE,MAAM,cAAc,CAAC;AAC3C,OAAO,EAAgB,UAAU,EAAE,MAAM,aAAa,CAAC;AAEvD,OAAO,EAAE,iBAAiB,EAAoB,MAAM,iCAAiC,CAAC;AACtF,OAAO,EAAE,oBAAoB,EAAE,mBAAmB,EAAE,MAAM,8BAA8B,CAAC;AACzF,OAAO,EAAE,OAAO,EAAU,MAAM,eAAe,CAAC;AAgBhD,MAAM,CAAC,MAAM,8BAA8B,GAAgD,IAAI,UAAU,CAAgC;IACvI,UAAU,EAAE;QACV,MAAM,EAAE,EAAC,IAAI,EAAE,IAAI,WAAW,CAAC,EAAC,IAAI,EAAE,oBAAoB,EAAE,KAAK,EAAE,mBAAmB,CAAC,KAAK,EAAC,CAAC,EAAC;QAC/F,MAAM,EAAE,EAAC,IAAI,EAAE,OAAO,EAAC;QACvB,WAAW,EAAE,EAAC,IAAI,EAAE,iBAAiB,EAAC;KACvC;IACD,UAAU,EAAE,SAAS,CAAC,SAAS;CAChC,CAAC,CAAC"}