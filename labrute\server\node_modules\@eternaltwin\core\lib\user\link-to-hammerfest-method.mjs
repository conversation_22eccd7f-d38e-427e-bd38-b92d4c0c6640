import { TsEnumType } from "kryo/ts-enum";
/**
 * Tag identifying the Hammerfest linking method.
 */
export var LinkToHammerfestMethod;
(function (LinkToHammerfestMethod) {
    LinkToHammerfestMethod[LinkToHammerfestMethod["Credentials"] = 0] = "Credentials";
    LinkToHammerfestMethod[LinkToHammerfestMethod["SessionKey"] = 1] = "SessionKey";
    LinkToHammerfestMethod[LinkToHammerfestMethod["Ref"] = 2] = "Ref";
})(LinkToHammerfestMethod || (LinkToHammerfestMethod = {}));
export const $LinkToHammerfestMethod = new TsEnumType({
    enum: LinkToHammerfestMethod,
});
//# sourceMappingURL=link-to-hammerfest-method.mjs.map