import { RecordIoType } from "kryo/record";
import { HammerfestServer } from "../hammerfest/hammerfest-server.mjs";
import { HammerfestUserId } from "../hammerfest/hammerfest-user-id.mjs";
import { LinkToHammerfestMethod } from "./link-to-hammerfest-method.mjs";
import { UserId } from "./user-id.mjs";
export interface LinkToHammerfestWithRefOptions {
    method: LinkToHammerfestMethod.Ref;
    /**
     * Id of the Eternaltwin user to link.
     */
    userId: UserId;
    /**
     * Hammerfest server.
     */
    hammerfestServer: HammerfestServer;
    /**
     * User id for the Hammerfest user.
     */
    hammerfestUserId: HammerfestUserId;
}
export declare const $LinkToHammerfestWithRefOptions: RecordIoType<LinkToHammerfestWithRefOptions>;
