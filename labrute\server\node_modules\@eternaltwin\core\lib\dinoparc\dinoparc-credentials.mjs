import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $DinoparcPassword } from "./dinoparc-password.mjs";
import { $DinoparcServer } from "./dinoparc-server.mjs";
import { $DinoparcUsername } from "./dinoparc-username.mjs";
export const $DinoparcCredentials = new RecordType({
    properties: {
        server: { type: $DinoparcServer },
        username: { type: $DinoparcUsername },
        password: { type: $DinoparcPassword },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=dinoparc-credentials.mjs.map