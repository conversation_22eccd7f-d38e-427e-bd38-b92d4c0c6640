import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { RecordType } from "kryo/record";
import { $UserId } from "../user/user-id.mjs";
import { $OauthClientId } from "./oauth-client-id.mjs";
import { $RfcOauthAccessTokenKey } from "./rfc-oauth-access-token-key.mjs";
export const $StoredOauthAccessToken = new RecordType({
    properties: {
        key: { type: $RfcOauthAccessTokenKey },
        ctime: { type: $Date },
        expirationTime: { type: $Date },
        userId: { type: $UserId },
        clientId: { type: $OauthClientId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=create-stored-oauth-access-token-options.mjs.map