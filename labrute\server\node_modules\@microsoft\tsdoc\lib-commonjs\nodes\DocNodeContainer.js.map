{"version": 3, "file": "DocNodeContainer.js", "sourceRoot": "", "sources": ["../../src/nodes/DocNodeContainer.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,qCAAkF;AAYlF;;;GAGG;AACH;IAA+C,oCAAO;IAGpD;;;OAGG;IACH,0BACE,UAA2E,EAC3E,UAAmC;QAFrC,YAIE,kBAAM,UAAU,CAAC,SAKlB;QAfgB,YAAM,GAAc,EAAE,CAAC;QAYtC,IAAI,UAAU,KAAK,SAAS,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE;YACrD,KAAI,CAAC,WAAW,CAAC,UAAU,CAAC,CAAC;SAC9B;;IACH,CAAC;IAKD,sBAAW,mCAAK;QAHhB;;WAEG;aACH;YACE,OAAO,IAAI,CAAC,MAAM,CAAC;QACrB,CAAC;;;OAAA;IAED;;OAEG;IACI,qCAAU,GAAjB,UAAkB,OAAgB;QAChC,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE;YAC9E,MAAM,IAAI,KAAK,CACb,6CAA2C,IAAI,CAAC,IAAI,aAAU;iBAC5D,6BAA2B,OAAO,CAAC,IAAM,CAAA,CAC5C,CAAC;SACH;QAED,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;IAC7B,CAAC;IAED;;OAEG;IACI,sCAAW,GAAlB,UAAmB,QAAgC;QACjD,KAAsB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE;YAA3B,IAAM,OAAO,iBAAA;YAChB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;SAC1B;IACH,CAAC;IAED;;OAEG;IACI,qCAAU,GAAjB;QACE,IAAI,CAAC,MAAO,CAAC,MAAM,GAAG,CAAC,CAAC;IAC1B,CAAC;IAED,gBAAgB;IACN,0CAAe,GAAzB;QACE,OAAO,IAAI,CAAC,MAAM,CAAC;IACrB,CAAC;IACH,uBAAC;AAAD,CAAC,AA3DD,CAA+C,iBAAO,GA2DrD;AA3DqB,4CAAgB", "sourcesContent": ["import { DocNode, IDocNodeParameters, IDocNodeParsedParameters } from './DocNode';\r\n\r\n/**\r\n * Constructor parameters for {@link DocNodeContainer}.\r\n */\r\nexport interface IDocNodeContainerParameters extends IDocNodeParameters {}\r\n\r\n/**\r\n * Constructor parameters for {@link DocNodeContainer}.\r\n */\r\nexport interface IDocNodeContainerParsedParameters extends IDocNodeParsedParameters {}\r\n\r\n/**\r\n * DocNodeContainer is the base class for DocNode classes that allow arbitrary child nodes to be added by the consumer.\r\n * The child classes are {@link DocParagraph} and {@link DocSection}.\r\n */\r\nexport abstract class DocNodeContainer extends DocNode {\r\n  private readonly _nodes: DocNode[] = [];\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(\r\n    parameters: IDocNodeContainerParameters | IDocNodeContainerParsedParameters,\r\n    childNodes?: ReadonlyArray<DocNode>\r\n  ) {\r\n    super(parameters);\r\n\r\n    if (childNodes !== undefined && childNodes.length > 0) {\r\n      this.appendNodes(childNodes);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * The nodes that were added to this container.\r\n   */\r\n  public get nodes(): ReadonlyArray<DocNode> {\r\n    return this._nodes;\r\n  }\r\n\r\n  /**\r\n   * Append a node to the container.\r\n   */\r\n  public appendNode(docNode: DocNode): void {\r\n    if (!this.configuration.docNodeManager.isAllowedChild(this.kind, docNode.kind)) {\r\n      throw new Error(\r\n        `The TSDocConfiguration does not allow a ${this.kind} node to` +\r\n          ` contain a node of type ${docNode.kind}`\r\n      );\r\n    }\r\n\r\n    this._nodes!.push(docNode);\r\n  }\r\n\r\n  /**\r\n   * Append nodes to the container.\r\n   */\r\n  public appendNodes(docNodes: ReadonlyArray<DocNode>): void {\r\n    for (const docNode of docNodes) {\r\n      this.appendNode(docNode);\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Remove all nodes from the container.\r\n   */\r\n  public clearNodes(): void {\r\n    this._nodes!.length = 0;\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return this._nodes;\r\n  }\r\n}\r\n"]}