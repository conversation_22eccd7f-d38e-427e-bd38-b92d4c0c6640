import { RecordIoType } from "kryo/record";
import { RfcOauthAccessTokenKey } from "../oauth/rfc-oauth-access-token-key.mjs";
import { RfcOauthRefreshTokenKey } from "../oauth/rfc-oauth-refresh-token-key.mjs";
import { TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
/**
 * Options when touching a Twinoid OAuth token.
 */
export interface TouchOauthTokenOptions {
    accessToken: RfcOauthAccessTokenKey;
    refreshToken?: RfcOauthRefreshTokenKey;
    expirationTime: Date;
    twinoidUserId: TwinoidUserId;
}
export declare const $TouchOauthTokenOptions: RecordIoType<TouchOauthTokenOptions>;
