import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { RecordType } from "kryo/record";
import { $TwinoidUserId } from "./twinoid-user-id.mjs";
export const $GetTwinoidUserOptions = new RecordType({
    properties: {
        id: { type: $TwinoidUserId },
        time: { type: $Date, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=get-twinoid-user-options.mjs.map