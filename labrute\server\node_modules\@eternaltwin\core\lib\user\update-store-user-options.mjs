import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $UpdateStoreUserPatch } from "./update-store-user-patch.mjs";
import { $UserIdRef } from "./user-id-ref.mjs";
export const $UpdateStoreUserOptions = new RecordType({
    properties: {
        ref: { type: $UserIdRef },
        actor: { type: $UserIdRef },
        patch: { type: $UpdateStoreUserPatch },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=update-store-user-options.mjs.map