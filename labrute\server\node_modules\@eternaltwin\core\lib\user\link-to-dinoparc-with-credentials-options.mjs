import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $DinoparcPassword } from "../dinoparc/dinoparc-password.mjs";
import { $DinoparcServer } from "../dinoparc/dinoparc-server.mjs";
import { $DinoparcUsername } from "../dinoparc/dinoparc-username.mjs";
import { $LinkToDinoparcMethod, LinkToDinoparcMethod } from "./link-to-dinoparc-method.mjs";
import { $UserId } from "./user-id.mjs";
export const $LinkToDinoparcWithCredentialsOptions = new RecordType({
    properties: {
        method: { type: new LiteralType({ type: $LinkToDinoparcMethod, value: LinkToDinoparcMethod.Credentials }) },
        userId: { type: $UserId },
        dinoparcServer: { type: $DinoparcServer },
        dinoparcUsername: { type: $DinoparcUsername },
        dinoparcPassword: { type: $DinoparcPassword },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=link-to-dinoparc-with-credentials-options.mjs.map