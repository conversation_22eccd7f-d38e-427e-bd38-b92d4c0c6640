import { CaseStyle } from "kryo";
import { $Boolean } from "kryo/boolean";
import { $Date } from "kryo/date";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $FieldVersions } from "../core/field-versions.mjs";
import { $NullableDate } from "../core/nullable-date.mjs";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $VersionedLinks } from "../link/versioned-links.mjs";
import { $UserDisplayName } from "./user-display-name.mjs";
import { $UserId } from "./user-id.mjs";
export const $User = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.User }) },
        id: { type: $UserId },
        createdAt: { type: $Date },
        deletedAt: { type: $NullableDate },
        displayName: { type: $FieldVersions.apply($UserDisplayName) },
        isAdministrator: { type: $Boolean },
        links: { type: $VersionedLinks },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=user.mjs.map