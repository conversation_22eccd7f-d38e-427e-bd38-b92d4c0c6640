import { CaseStyle } from "kryo";
import { ArrayType } from "kryo/array";
import { $Uint32 } from "kryo/integer";
import { RecordType } from "kryo/record";
import { $DinoparcSessionUser } from "./dinoparc-session-user.mjs";
import { $ShortDinoparcDinozWithLevel } from "./short-dinoparc-dinoz-with-level.mjs";
import { $ShortDinoparcUser } from "./short-dinoparc-user.mjs";
export const $DinoparcExchangeWithResponse = new RecordType({
    properties: {
        sessionUser: { type: $DinoparcSessionUser },
        ownBills: { type: $Uint32 },
        ownDinoz: { type: new ArrayType({ itemType: $ShortDinoparcDinozWithLevel, maxLength: 20000 }) },
        otherUser: { type: $ShortDinoparcUser },
        otherDinoz: { type: new ArrayType({ itemType: $ShortDinoparcDinozWithLevel, maxLength: 20000 }) },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=dinoparc-exchange-with-response.mjs.map