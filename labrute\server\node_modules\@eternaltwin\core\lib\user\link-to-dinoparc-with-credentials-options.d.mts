import { RecordIoType } from "kryo/record";
import { DinoparcPassword } from "../dinoparc/dinoparc-password.mjs";
import { DinoparcServer } from "../dinoparc/dinoparc-server.mjs";
import { <PERSON>parcUsername } from "../dinoparc/dinoparc-username.mjs";
import { LinkToDinoparcMethod } from "./link-to-dinoparc-method.mjs";
import { UserId } from "./user-id.mjs";
export interface LinkToDinoparcWithCredentialsOptions {
    method: LinkToDinoparcMethod.Credentials;
    /**
     * Id of the Eternaltwin user to link.
     */
    userId: UserId;
    /**
     * Dinoparc server.
     */
    dinoparcServer: DinoparcServer;
    /**
     * Username for the Dinoparc user.
     */
    dinoparcUsername: DinoparcUsername;
    /**
     * Password for the Dinoparc user.
     */
    dinoparcPassword: DinoparcPassword;
}
export declare const $LinkToDinoparcWithCredentialsOptions: RecordIoType<LinkToDinoparcWithCredentialsOptions>;
