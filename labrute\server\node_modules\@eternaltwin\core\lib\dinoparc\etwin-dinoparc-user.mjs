import { CaseStyle } from "kryo";
import { ArrayType } from "kryo/array";
import { $Date } from "kryo/date";
import { $Uint32 } from "kryo/integer";
import { LiteralType } from "kryo/literal";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $VersionedEtwinLink } from "../link/versioned-etwin-link.mjs";
import { $NullableLatestTemporal } from "../temporal/latest-temporal.mjs";
import { $DinoparcCollection } from "./dinoparc-collection.mjs";
import { $DinoparcDinozIdRef } from "./dinoparc-dinoz-id-ref.mjs";
import { $DinoparcItemCounts } from "./dinoparc-item-counts.mjs";
import { $DinoparcServer } from "./dinoparc-server.mjs";
import { $DinoparcUserId } from "./dinoparc-user-id.mjs";
import { $DinoparcUsername } from "./dinoparc-username.mjs";
export const $EtwinDinoparcUser = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.DinoparcUser }) },
        server: { type: $DinoparcServer },
        id: { type: $DinoparcUserId },
        archivedAt: { type: $Date },
        username: { type: $DinoparcUsername },
        coins: { type: $NullableLatestTemporal.apply($Uint32) },
        dinoz: { type: $NullableLatestTemporal.apply(new ArrayType({ itemType: $DinoparcDinozIdRef, maxLength: 10000 })) },
        inventory: { type: $NullableLatestTemporal.apply($DinoparcItemCounts) },
        collection: { type: $NullableLatestTemporal.apply($DinoparcCollection) },
        etwin: { type: $VersionedEtwinLink },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableEtwinDinoparcUser = new TryUnionType({ variants: [$Null, $EtwinDinoparcUser] });
//# sourceMappingURL=etwin-dinoparc-user.mjs.map