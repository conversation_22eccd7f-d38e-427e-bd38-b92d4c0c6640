import { RecordIoType } from "kryo/record";
import { PeriodLower } from "../core/period-lower.mjs";
export interface GetOutboundEmails {
    /**
     * API time, for time-travel querying
     */
    time?: Date;
    /**
     * Filter emails created in the provided period
     */
    createdAt?: PeriodLower;
    /**
     * Maximum number of results to return
     */
    limit: number;
}
export declare const $GetOutboundEmails: RecordIoType<GetOutboundEmails>;
