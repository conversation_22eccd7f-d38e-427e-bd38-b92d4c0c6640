import { TryUnionType } from "kryo/try-union";
import { OauthClientBareKey } from "./oauth-client-bare-key.mjs";
import { OauthClientKey } from "./oauth-client-key.mjs";
/**
 * Represents an OAuth client key (stable id) where the suffix `@clients` is optional.
 *
 * Internally, the key is always typed (with the suffix). The bare (untyped) version is only used when reading some
 * URIs or for internal storage.
 */
export type OauthClientInputKey = OauthClientBareKey | OauthClientKey;
export declare const $OauthClientInputKey: TryUnionType<OauthClientInputKey>;
