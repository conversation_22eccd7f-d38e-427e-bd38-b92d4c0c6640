import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $EmailAddress } from "../email/email-address.mjs";
import { $UserId } from "./user-id.mjs";
import { $Username } from "./username.mjs";
export const $UserRef = new RecordType({
    properties: {
        id: { type: $UserId, optional: true },
        username: { type: $Username, optional: true },
        email: { type: $EmailAddress, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=user-ref.mjs.map