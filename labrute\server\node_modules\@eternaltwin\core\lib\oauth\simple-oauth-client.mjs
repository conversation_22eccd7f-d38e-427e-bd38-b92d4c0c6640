import { $Url } from "../core/url.mjs";
import { $NullableUserIdRef } from "../user/user-id-ref.mjs";
import { $ShortOauthClient } from "./short-oauth-client.mjs";
export const $SimpleOauthClient = $ShortOauthClient.extend({
    properties: {
        appUri: { type: $Url },
        callbackUri: { type: $Url },
        owner: { type: $NullableUserIdRef },
    }
});
//# sourceMappingURL=simple-oauth-client.mjs.map