import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $FieldShortVersions } from "../core/field-short-versions.mjs";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $UserDisplayName } from "./user-display-name.mjs";
import { $UserId } from "./user-id.mjs";
export const $ShortUser = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.User }) },
        id: { type: $UserId },
        displayName: { type: $FieldShortVersions.apply($UserDisplayName) },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableShortUser = new TryUnionType({ variants: [$Null, $ShortUser] });
//# sourceMappingURL=short-user.mjs.map