import { $Null } from "kryo/null";
import { TryUnionType } from "kryo/try-union";
import { Ucs2StringType } from "kryo/ucs2-string";
/**
 * We only check that the adress is trimmed and non-empty, but leave-out verification.
 * (We only check for the `@` symbol).
 */
export const $EmailAddress = new Ucs2StringType({
    trimmed: true,
    minLength: 1,
    maxLength: 100,
    pattern: /@/,
});
export const $NullableEmailAddress = new TryUnionType({ variants: [$Null, $EmailAddress] });
//# sourceMappingURL=email-address.mjs.map