import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
import { $LinkToTwinoidMethod, LinkToTwinoidMethod } from "./link-to-twinoid-method.mjs";
import { $UserId } from "./user-id.mjs";
export const $LinkToTwinoidWithRefOptions = new RecordType({
    properties: {
        method: { type: new LiteralType({ type: $LinkToTwinoidMethod, value: LinkToTwinoidMethod.Ref }) },
        userId: { type: $UserId },
        twinoidUserId: { type: $TwinoidUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=link-to-twinoid-with-ref-options.mjs.map