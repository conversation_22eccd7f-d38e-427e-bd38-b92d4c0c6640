import { TryUnionType } from "kryo/try-union";
import { OauthClientBare<PERSON>ey } from "./oauth-client-bare-key.mjs";
import { OauthClientId } from "./oauth-client-id.mjs";
import { OauthClient<PERSON>ey } from "./oauth-client-key.mjs";
/**
 * Any identifier-like string for Eternaltwin's Oauth clients.
 */
export type OauthClientInputRef = OauthClientBareKey | OauthClientId | OauthClientKey;
export declare const $OauthClientInputRef: TryUnionType<OauthClientInputRef>;
