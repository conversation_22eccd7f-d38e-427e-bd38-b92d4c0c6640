import { TryUnionType } from "kryo/try-union";
import { CompleteSimpleUser } from "./complete-simple-user.mjs";
import { ShortUser } from "./short-user.mjs";
import { SimpleUser } from "./simple-user.mjs";
export type GetUserResult = ShortUser | SimpleUser | CompleteSimpleUser;
export declare const $GetUserResult: TryUnionType<GetUserResult>;
export type NullableGetUserResult = null | GetUserResult;
export declare const $NullableGetUserResult: TryUnionType<NullableGetUserResult>;
