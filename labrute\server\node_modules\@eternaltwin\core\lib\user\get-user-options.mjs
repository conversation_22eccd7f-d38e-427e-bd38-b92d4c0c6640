import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { RecordType } from "kryo/record";
import { $UserFields } from "./user-fields.mjs";
import { $UserRef } from "./user-ref.mjs";
export const $GetUserOptions = new RecordType({
    properties: {
        ref: { type: $UserRef },
        fields: { type: $UserFields },
        time: { type: $Date, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=get-user-options.mjs.map