import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $UserFieldsType, UserFieldsType } from "./user-fields-type.mjs";
import { $UserId } from "./user-id.mjs";
export const $CompleteIfSelfUserFields = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $UserFieldsType, value: UserFieldsType.CompleteIfSelf }) },
        selfUserId: { type: $UserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=complete-if-self-user-fields.mjs.map