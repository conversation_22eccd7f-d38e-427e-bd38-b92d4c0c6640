import { CaseStyle } from "kryo";
import { $Boolean } from "kryo/boolean";
import { $Date } from "kryo/date";
import { $Sint16, $Uint8, $Uint16 } from "kryo/integer";
import { LiteralType } from "kryo/literal";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $Ucs2String } from "kryo/ucs2-string";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $NullableLatestTemporal } from "../temporal/latest-temporal.mjs";
import { $DinoparcDinozElements } from "./dinoparc-dinoz-elements.mjs";
import { $DinoparcDinozId } from "./dinoparc-dinoz-id.mjs";
import { $NullableDinoparcDinozName } from "./dinoparc-dinoz-name.mjs";
import { $DinoparcDinozRace } from "./dinoparc-dinoz-race.mjs";
import { $DinoparcLocationId } from "./dinoparc-location-id.mjs";
import { $DinoparcServer } from "./dinoparc-server.mjs";
import { $DinoparcSkillLevels } from "./dinoparc-skill-levels.mjs";
import { $ShortDinoparcUser } from "./short-dinoparc-user.mjs";
export const $EtwinDinoparcDinoz = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.DinoparcDinoz }) },
        server: { type: $DinoparcServer },
        id: { type: $DinoparcDinozId },
        archivedAt: { type: $Date },
        name: { type: $NullableLatestTemporal.apply($NullableDinoparcDinozName) },
        owner: { type: $NullableLatestTemporal.apply($ShortDinoparcUser) },
        location: { type: $NullableLatestTemporal.apply($DinoparcLocationId) },
        race: { type: $NullableLatestTemporal.apply($DinoparcDinozRace) },
        skin: { type: $NullableLatestTemporal.apply($Ucs2String) },
        life: { type: $NullableLatestTemporal.apply($Uint8) },
        level: { type: $NullableLatestTemporal.apply($Uint16) },
        experience: { type: $NullableLatestTemporal.apply($Uint8) },
        danger: { type: $NullableLatestTemporal.apply($Sint16) },
        inTournament: { type: $NullableLatestTemporal.apply($Boolean) },
        elements: { type: $NullableLatestTemporal.apply($DinoparcDinozElements) },
        skills: { type: $NullableLatestTemporal.apply($DinoparcSkillLevels) },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableEtwinDinoparcDinoz = new TryUnionType({ variants: [$Null, $EtwinDinoparcDinoz] });
//# sourceMappingURL=etwin-dinoparc-dinoz.mjs.map