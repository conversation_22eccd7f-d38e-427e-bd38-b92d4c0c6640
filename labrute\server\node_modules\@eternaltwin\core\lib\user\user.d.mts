import { RecordIoType } from "kryo/record";
import { FieldVersions } from "../core/field-versions.mjs";
import { NullableDate } from "../core/nullable-date.mjs";
import { ObjectType } from "../core/object-type.mjs";
import { VersionedLinks } from "../link/versioned-links.mjs";
import { UserDisplayName } from "./user-display-name.mjs";
import { UserId } from "./user-id.mjs";
/**
 * Main user interface.
 *
 * Aggregates data from multiple services to present the full user.
 */
export interface User {
    type: ObjectType.User;
    id: UserId;
    createdAt: Date;
    deletedAt: NullableDate;
    displayName: FieldVersions<UserDisplayName>;
    isAdministrator: boolean;
    links: VersionedLinks;
}
export declare const $User: RecordIoType<User>;
