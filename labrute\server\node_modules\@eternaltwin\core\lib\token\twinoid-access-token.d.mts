import { RecordIoType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { RfcOauthAccessTokenKey } from "../oauth/rfc-oauth-access-token-key.mjs";
import { TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
export interface TwinoidAccessToken {
    key: RfcOauthAccessTokenKey;
    ctime: Date;
    atime: Date;
    expirationTime: Date;
    twinoidUserId: TwinoidUserId;
}
export declare const $TwinoidAccessToken: RecordIoType<TwinoidAccessToken>;
export type NullableTwinoidAccessToken = null | TwinoidAccessToken;
export declare const $NullableTwinoidAccessToken: TryUnionType<NullableTwinoidAccessToken>;
