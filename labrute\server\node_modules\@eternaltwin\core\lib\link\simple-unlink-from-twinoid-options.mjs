import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
import { $UserId } from "../user/user-id.mjs";
export const $SimpleUnlinkFromTwinoidOptions = new RecordType({
    properties: {
        userId: { type: $UserId },
        twinoidUserId: { type: $TwinoidUserId },
        unlinkedBy: { type: $UserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=simple-unlink-from-twinoid-options.mjs.map