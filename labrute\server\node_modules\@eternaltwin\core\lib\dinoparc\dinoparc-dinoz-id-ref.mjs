import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $DinoparcDinozId } from "./dinoparc-dinoz-id.mjs";
import { $DinoparcServer } from "./dinoparc-server.mjs";
export const $DinoparcDinozIdRef = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.DinoparcDinoz }) },
        server: { type: $DinoparcServer },
        id: { type: $DinoparcDinozId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=dinoparc-dinoz-id-ref.mjs.map