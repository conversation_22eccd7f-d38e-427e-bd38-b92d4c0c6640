import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $NullablePassword } from "../password/password.mjs";
import { $UserDisplayName } from "./user-display-name.mjs";
import { $NullableUsername } from "./username.mjs";
export const $UpdateUserPatch = new RecordType({
    properties: {
        displayName: { type: $UserDisplayName, optional: true },
        username: { type: $NullableUsername, optional: true },
        password: { type: $NullablePassword, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=update-user-patch.mjs.map