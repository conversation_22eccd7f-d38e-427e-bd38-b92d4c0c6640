"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ParserContext = void 0;
var TextRange_1 = require("./TextRange");
var nodes_1 = require("../nodes");
var ParserMessageLog_1 = require("./ParserMessageLog");
/**
 * An internal data structure that tracks all the state being built up by the various
 * parser stages.
 */
var ParserContext = /** @class */ (function () {
    function ParserContext(configuration, sourceRange) {
        /**
         * The text range starting from the opening `/**` and ending with
         * the closing `*\/` delimiter.
         */
        this.commentRange = TextRange_1.TextRange.empty;
        /**
         * The text ranges corresponding to the lines of content inside the comment.
         */
        this.lines = [];
        /**
         * A complete list of all tokens that were extracted from the input lines.
         */
        this.tokens = [];
        this.configuration = configuration;
        this.sourceRange = sourceRange;
        this.docComment = new nodes_1.DocComment({ configuration: this.configuration });
        this.log = new ParserMessageLog_1.ParserMessageLog();
    }
    return ParserContext;
}());
exports.ParserContext = ParserContext;
//# sourceMappingURL=ParserContext.js.map