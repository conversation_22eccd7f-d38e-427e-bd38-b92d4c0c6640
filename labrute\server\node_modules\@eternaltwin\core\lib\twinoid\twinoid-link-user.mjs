import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $TwinoidSiteUserId } from "./twinoid-site-user-id.mjs";
export const $TwinoidLinkUser = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.TwinoidSiteUser }) },
        id: { type: $TwinoidSiteUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableTwinoidLinkUser = new TryUnionType({ variants: [$Null, $TwinoidLinkUser] });
//# sourceMappingURL=twinoid-link-user.mjs.map