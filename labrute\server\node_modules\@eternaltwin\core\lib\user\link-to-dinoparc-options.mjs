import { TaggedUnionType } from "kryo/tagged-union";
import { $LinkToDinoparcWithCredentialsOptions } from "./link-to-dinoparc-with-credentials-options.mjs";
import { $LinkToDinoparcWithRefOptions } from "./link-to-dinoparc-with-ref-options.mjs";
export const $LinkToDinoparcOptions = new TaggedUnionType({
    variants: [$LinkToDinoparcWithCredentialsOptions, $LinkToDinoparcWithRefOptions],
    tag: "method",
});
//# sourceMappingURL=link-to-dinoparc-options.mjs.map