import { $Null } from "kryo/null";
import { TryUnionType } from "kryo/try-union";
import { Ucs2StringType } from "kryo/ucs2-string";
export const $DinoparcDinozName = new Ucs2StringType({
    trimmed: false,
    minLength: 1,
    maxLength: 50,
    pattern: /^.{1,50}$/,
});
export const $NullableDinoparcDinozName = new TryUnionType({ variants: [$Null, $DinoparcDinozName] });
//# sourceMappingURL=dinoparc-dinoz-name.mjs.map