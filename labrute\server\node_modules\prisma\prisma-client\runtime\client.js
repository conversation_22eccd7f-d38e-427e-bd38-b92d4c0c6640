"use strict";var Xa=Object.create;var lr=Object.defineProperty;var el=Object.getOwnPropertyDescriptor;var tl=Object.getOwnPropertyNames;var rl=Object.getPrototypeOf,nl=Object.prototype.hasOwnProperty;var Ci=e=>{throw TypeError(e)};var ie=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),yt=(e,t)=>{for(var r in t)lr(e,r,{get:t[r],enumerable:!0})},Ai=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of tl(t))!nl.call(e,i)&&i!==r&&lr(e,i,{get:()=>t[i],enumerable:!(n=el(t,i))||n.enumerable});return e};var B=(e,t,r)=>(r=e!=null?Xa(rl(e)):{},Ai(t||!e||!e.__esModule?lr(r,"default",{value:e,enumerable:!0}):r,e)),il=e=>Ai(lr({},"__esModule",{value:!0}),e);var Ri=(e,t,r)=>t.has(e)||Ci("Cannot "+r);var mn=(e,t,r)=>(Ri(e,t,"read from private field"),r?r.call(e):t.get(e)),Si=(e,t,r)=>t.has(e)?Ci("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),ki=(e,t,r,n)=>(Ri(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r);var Ui=ie((qd,kl)=>{kl.exports={name:"@prisma/internals",version:"6.4.1",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@antfu/ni":"0.21.12","@babel/helper-validator-identifier":"7.24.7","@opentelemetry/api":"1.9.0","@swc/core":"1.2.204","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.31","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"2.1.0",dotenv:"16.4.7",esbuild:"0.24.2","escape-string-regexp":"4.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"5.0.0","fp-ts":"2.16.9","fs-extra":"11.1.1","fs-jetpack":"5.1.0","global-dirs":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0","read-package-up":"11.0.0","replace-string":"3.1.0",resolve:"1.22.10","string-width":"4.2.3","strip-ansi":"6.0.1","strip-indent":"3.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"2.1.1",tmp:"0.2.3","ts-node":"10.9.2","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.4.0-29.a9055b89e58b4b5bfb59600785423b1db3d0e75d","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var Bi=ie((jd,Il)=>{Il.exports={name:"dotenv",version:"16.4.7",description:"Loads environment variables from .env file",main:"lib/main.js",types:"lib/main.d.ts",exports:{".":{types:"./lib/main.d.ts",require:"./lib/main.js",default:"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},scripts:{"dts-check":"tsc --project tests/types/tsconfig.json",lint:"standard",pretest:"npm run lint && npm run dts-check",test:"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=lcov",prerelease:"npm test",release:"standard-version"},repository:{type:"git",url:"git://github.com/motdotla/dotenv.git"},funding:"https://dotenvx.com",keywords:["dotenv","env",".env","environment","variables","config","settings"],readmeFilename:"README.md",license:"BSD-2-Clause",devDependencies:{"@types/node":"^18.11.3",decache:"^4.6.2",sinon:"^14.0.1",standard:"^17.0.0","standard-version":"^9.5.0",tap:"^19.2.0",typescript:"^4.8.4"},engines:{node:">=12"},browser:{fs:!1}}});var Wi=ie((Ud,xe)=>{"use strict";var vn=require("fs"),Pn=require("path"),Dl=require("os"),_l=require("crypto"),Nl=Bi(),Tn=Nl.version,Ml=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function Fl(e){let t={},r=e.toString();r=r.replace(/\r\n?/mg,`
`);let n;for(;(n=Ml.exec(r))!=null;){let i=n[1],o=n[2]||"";o=o.trim();let s=o[0];o=o.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),s==='"'&&(o=o.replace(/\\n/g,`
`),o=o.replace(/\\r/g,"\r")),t[i]=o}return t}function Ll(e){let t=Ji(e),r=L.configDotenv({path:t});if(!r.parsed){let s=new Error(`MISSING_DATA: Cannot parse ${t} for an unknown reason`);throw s.code="MISSING_DATA",s}let n=Gi(e).split(","),i=n.length,o;for(let s=0;s<i;s++)try{let a=n[s].trim(),l=Vl(r,a);o=L.decrypt(l.ciphertext,l.key);break}catch(a){if(s+1>=i)throw a}return L.parse(o)}function $l(e){console.log(`[dotenv@${Tn}][INFO] ${e}`)}function ql(e){console.log(`[dotenv@${Tn}][WARN] ${e}`)}function cr(e){console.log(`[dotenv@${Tn}][DEBUG] ${e}`)}function Gi(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function Vl(e,t){let r;try{r=new URL(t)}catch(a){if(a.code==="ERR_INVALID_URL"){let l=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw l.code="INVALID_DOTENV_KEY",l}throw a}let n=r.password;if(!n){let a=new Error("INVALID_DOTENV_KEY: Missing key part");throw a.code="INVALID_DOTENV_KEY",a}let i=r.searchParams.get("environment");if(!i){let a=new Error("INVALID_DOTENV_KEY: Missing environment part");throw a.code="INVALID_DOTENV_KEY",a}let o=`DOTENV_VAULT_${i.toUpperCase()}`,s=e.parsed[o];if(!s){let a=new Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${o} in your .env.vault file.`);throw a.code="NOT_FOUND_DOTENV_ENVIRONMENT",a}return{ciphertext:s,key:n}}function Ji(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let r of e.path)vn.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=Pn.resolve(process.cwd(),".env.vault");return vn.existsSync(t)?t:null}function Qi(e){return e[0]==="~"?Pn.join(Dl.homedir(),e.slice(1)):e}function jl(e){$l("Loading env from encrypted .env.vault");let t=L._parseVault(e),r=process.env;return e&&e.processEnv!=null&&(r=e.processEnv),L.populate(r,t,e),{parsed:t}}function Ul(e){let t=Pn.resolve(process.cwd(),".env"),r="utf8",n=!!(e&&e.debug);e&&e.encoding?r=e.encoding:n&&cr("No encoding is specified. UTF-8 is used by default");let i=[t];if(e&&e.path)if(!Array.isArray(e.path))i=[Qi(e.path)];else{i=[];for(let l of e.path)i.push(Qi(l))}let o,s={};for(let l of i)try{let u=L.parse(vn.readFileSync(l,{encoding:r}));L.populate(s,u,e)}catch(u){n&&cr(`Failed to load ${l} ${u.message}`),o=u}let a=process.env;return e&&e.processEnv!=null&&(a=e.processEnv),L.populate(a,s,e),o?{parsed:s,error:o}:{parsed:s}}function Bl(e){if(Gi(e).length===0)return L.configDotenv(e);let t=Ji(e);return t?L._configVault(e):(ql(`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`),L.configDotenv(e))}function Ql(e,t){let r=Buffer.from(t.slice(-64),"hex"),n=Buffer.from(e,"base64"),i=n.subarray(0,12),o=n.subarray(-16);n=n.subarray(12,-16);try{let s=_l.createDecipheriv("aes-256-gcm",r,i);return s.setAuthTag(o),`${s.update(n)}${s.final()}`}catch(s){let a=s instanceof RangeError,l=s.message==="Invalid key length",u=s.message==="Unsupported state or unable to authenticate data";if(a||l){let c=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw c.code="INVALID_DOTENV_KEY",c}else if(u){let c=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw c.code="DECRYPTION_FAILED",c}else throw s}}function Gl(e,t,r={}){let n=!!(r&&r.debug),i=!!(r&&r.override);if(typeof t!="object"){let o=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw o.code="OBJECT_REQUIRED",o}for(let o of Object.keys(t))Object.prototype.hasOwnProperty.call(e,o)?(i===!0&&(e[o]=t[o]),n&&cr(i===!0?`"${o}" is already defined and WAS overwritten`:`"${o}" is already defined and was NOT overwritten`)):e[o]=t[o]}var L={configDotenv:Ul,_configVault:jl,_parseVault:Ll,config:Bl,decrypt:Ql,parse:Fl,populate:Gl};xe.exports.configDotenv=L.configDotenv;xe.exports._configVault=L._configVault;xe.exports._parseVault=L._parseVault;xe.exports.config=L.config;xe.exports.decrypt=L.decrypt;xe.exports.parse=L.parse;xe.exports.populate=L.populate;xe.exports=L});var Xi=ie((Kd,Zi)=>{"use strict";Zi.exports=e=>{let t=e.match(/^[ \t]*(?=\S)/gm);return t?t.reduce((r,n)=>Math.min(r,n.length),1/0):0}});var to=ie((zd,eo)=>{"use strict";var Kl=Xi();eo.exports=e=>{let t=Kl(e);if(t===0)return e;let r=new RegExp(`^[ \\t]{${t}}`,"gm");return e.replace(r,"")}});var io=ie((nm,no)=>{"use strict";no.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var ao=ie((om,so)=>{"use strict";so.exports=({onlyFirst:e=!1}={})=>{let t=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?\\u0007)","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-ntqry=><~]))"].join("|");return new RegExp(t,e?void 0:"g")}});var On=ie((sm,lo)=>{"use strict";var eu=ao();lo.exports=e=>typeof e=="string"?e.replace(eu(),""):e});var uo=ie((lm,mr)=>{"use strict";mr.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};mr.exports.default=mr.exports});var qn=ie((wf,_o)=>{"use strict";_o.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,l,u,c,p,d,f,g,h,R,v,T,P,k=[];for(l=0;l<i;l++)k.push(l+1),k.push(t.charCodeAt(s+l));for(var A=k.length-1;a<o-3;)for(R=r.charCodeAt(s+(u=a)),v=r.charCodeAt(s+(c=a+1)),T=r.charCodeAt(s+(p=a+2)),P=r.charCodeAt(s+(d=a+3)),f=a+=4,l=0;l<A;l+=2)g=k[l],h=k[l+1],u=e(g,u,c,R,h),c=e(u,c,p,v,h),p=e(c,p,d,T,h),f=e(p,d,f,P,h),k[l]=f,d=p,p=c,c=u,u=g;for(;a<o;)for(R=r.charCodeAt(s+(u=a)),f=++a,l=0;l<A;l+=2)g=k[l],k[l]=f=e(g,u,f,R,k[l+1]),u=g;return f}}()});var cs=ie((ah,Jc)=>{Jc.exports={name:"@prisma/engines-version",version:"6.4.0-29.a9055b89e58b4b5bfb59600785423b1db3d0e75d",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"a9055b89e58b4b5bfb59600785423b1db3d0e75d"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var xd={};yt(xd,{Debug:()=>bn,Decimal:()=>me,Extensions:()=>fn,MetricsClient:()=>ot,PrismaClientInitializationError:()=>S,PrismaClientKnownRequestError:()=>Q,PrismaClientRustPanicError:()=>se,PrismaClientUnknownRequestError:()=>G,PrismaClientValidationError:()=>J,Public:()=>gn,Sql:()=>X,createParam:()=>es,defineDmmfProperty:()=>as,deserializeJsonResponse:()=>He,deserializeRawResult:()=>cn,dmmfToRuntimeDataModel:()=>ss,empty:()=>ds,getPrismaClient:()=>za,getRuntime:()=>Wr,join:()=>ps,makeStrictEnum:()=>Ya,makeTypedQueryFactory:()=>ls,objectEnumValues:()=>Dr,raw:()=>Kn,serializeJsonQuery:()=>qr,skip:()=>$r,sqltag:()=>zn,warnEnvConflicts:()=>Za,warnOnce:()=>St});module.exports=il(xd);var fn={};yt(fn,{defineExtension:()=>Oi,getExtensionContext:()=>Ii});function Oi(e){return typeof e=="function"?e:t=>t.$extends(e)}function Ii(e){return e}var gn={};yt(gn,{validator:()=>Di});function Di(...e){return t=>t}function wt(e){return{ok:!1,error:e,map(){return wt(e)},flatMap(){return wt(e)}}}var hn=class{constructor(){this.registeredErrors=[]}consumeError(t){return this.registeredErrors[t]}registerNewError(t){let r=0;for(;this.registeredErrors[r]!==void 0;)r++;return this.registeredErrors[r]={error:t},r}},yn=e=>{let t=new hn,r=ce(t,e.transactionContext.bind(e)),n={adapterName:e.adapterName,errorRegistry:t,queryRaw:ce(t,e.queryRaw.bind(e)),executeRaw:ce(t,e.executeRaw.bind(e)),provider:e.provider,transactionContext:async(...i)=>(await r(...i)).map(s=>ol(t,s))};return e.getConnectionInfo&&(n.getConnectionInfo=al(t,e.getConnectionInfo.bind(e))),n},ol=(e,t)=>{let r=ce(e,t.startTransaction.bind(t));return{adapterName:t.adapterName,provider:t.provider,queryRaw:ce(e,t.queryRaw.bind(t)),executeRaw:ce(e,t.executeRaw.bind(t)),startTransaction:async(...n)=>(await r(...n)).map(o=>sl(e,o))}},sl=(e,t)=>({adapterName:t.adapterName,provider:t.provider,options:t.options,queryRaw:ce(e,t.queryRaw.bind(t)),executeRaw:ce(e,t.executeRaw.bind(t)),commit:ce(e,t.commit.bind(t)),rollback:ce(e,t.rollback.bind(t))});function ce(e,t){return async(...r)=>{try{return await t(...r)}catch(n){let i=e.registerNewError(n);return wt({kind:"GenericJs",id:i})}}}function al(e,t){return(...r)=>{try{return t(...r)}catch(n){let i=e.registerNewError(n);return wt({kind:"GenericJs",id:i})}}}var ur={};yt(ur,{$:()=>Li,bgBlack:()=>yl,bgBlue:()=>xl,bgCyan:()=>Pl,bgGreen:()=>El,bgMagenta:()=>vl,bgRed:()=>wl,bgWhite:()=>Tl,bgYellow:()=>bl,black:()=>ml,blue:()=>Ne,bold:()=>H,cyan:()=>be,dim:()=>De,gray:()=>bt,green:()=>Et,grey:()=>hl,hidden:()=>pl,inverse:()=>cl,italic:()=>ul,magenta:()=>fl,red:()=>Ee,reset:()=>ll,strikethrough:()=>dl,underline:()=>Y,white:()=>gl,yellow:()=>_e});var wn,_i,Ni,Mi,Fi=!0;typeof process<"u"&&({FORCE_COLOR:wn,NODE_DISABLE_COLORS:_i,NO_COLOR:Ni,TERM:Mi}=process.env||{},Fi=process.stdout&&process.stdout.isTTY);var Li={enabled:!_i&&Ni==null&&Mi!=="dumb"&&(wn!=null&&wn!=="0"||Fi)};function _(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!Li.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var ll=_(0,0),H=_(1,22),De=_(2,22),ul=_(3,23),Y=_(4,24),cl=_(7,27),pl=_(8,28),dl=_(9,29),ml=_(30,39),Ee=_(31,39),Et=_(32,39),_e=_(33,39),Ne=_(34,39),fl=_(35,39),be=_(36,39),gl=_(37,39),bt=_(90,39),hl=_(90,39),yl=_(40,49),wl=_(41,49),El=_(42,49),bl=_(43,49),xl=_(44,49),vl=_(45,49),Pl=_(46,49),Tl=_(47,49);var Cl=100,$i=["green","yellow","blue","magenta","cyan","red"],xt=[],qi=Date.now(),Al=0,En=typeof process<"u"?process.env:{};globalThis.DEBUG??=En.DEBUG??"";globalThis.DEBUG_COLORS??=En.DEBUG_COLORS?En.DEBUG_COLORS==="true":!0;var vt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function Rl(e){let t={color:$i[Al++%$i.length],enabled:vt.enabled(e),namespace:e,log:vt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&xt.push([o,...n]),xt.length>Cl&&xt.shift(),vt.enabled(o)||i){let l=n.map(c=>typeof c=="string"?c:Sl(c)),u=`+${Date.now()-qi}ms`;qi=Date.now(),globalThis.DEBUG_COLORS?a(ur[s](H(o)),...l,ur[s](u)):a(o,...l,u)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var bn=new Proxy(Rl,{get:(e,t)=>vt[t],set:(e,t,r)=>vt[t]=r});function Sl(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function Vi(e=7500){let t=xt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function ji(){xt.length=0}var M=bn;var Ol=Ui(),xn=Ol.version;var An=B(Wi()),pr=B(require("fs"));var Be=B(require("path"));function Hi(e){let t=e.ignoreProcessEnv?{}:process.env,r=n=>n.match(/(.?\${(?:[a-zA-Z0-9_]+)?})/g)?.reduce(function(o,s){let a=/(.?)\${([a-zA-Z0-9_]+)?}/g.exec(s);if(!a)return o;let l=a[1],u,c;if(l==="\\")c=a[0],u=c.replace("\\$","$");else{let p=a[2];c=a[0].substring(l.length),u=Object.hasOwnProperty.call(t,p)?t[p]:e.parsed[p]||"",u=r(u)}return o.replace(c,u)},n)??n;for(let n in e.parsed){let i=Object.hasOwnProperty.call(t,n)?t[n]:e.parsed[n];e.parsed[n]=r(i)}for(let n in e.parsed)t[n]=e.parsed[n];return e}var Cn=M("prisma:tryLoadEnv");function Pt({rootEnvPath:e,schemaEnvPath:t},r={conflictCheck:"none"}){let n=Ki(e);r.conflictCheck!=="none"&&Jl(n,t,r.conflictCheck);let i=null;return zi(n?.path,t)||(i=Ki(t)),!n&&!i&&Cn("No Environment variables loaded"),i?.dotenvResult.error?console.error(Ee(H("Schema Env Error: "))+i.dotenvResult.error):{message:[n?.message,i?.message].filter(Boolean).join(`
`),parsed:{...n?.dotenvResult?.parsed,...i?.dotenvResult?.parsed}}}function Jl(e,t,r){let n=e?.dotenvResult.parsed,i=!zi(e?.path,t);if(n&&t&&i&&pr.default.existsSync(t)){let o=An.default.parse(pr.default.readFileSync(t)),s=[];for(let a in o)n[a]===o[a]&&s.push(a);if(s.length>0){let a=Be.default.relative(process.cwd(),e.path),l=Be.default.relative(process.cwd(),t);if(r==="error"){let u=`There is a conflict between env var${s.length>1?"s":""} in ${Y(a)} and ${Y(l)}
Conflicting env vars:
${s.map(c=>`  ${H(c)}`).join(`
`)}

We suggest to move the contents of ${Y(l)} to ${Y(a)} to consolidate your env vars.
`;throw new Error(u)}else if(r==="warn"){let u=`Conflict for env var${s.length>1?"s":""} ${s.map(c=>H(c)).join(", ")} in ${Y(a)} and ${Y(l)}
Env vars from ${Y(l)} overwrite the ones from ${Y(a)}
      `;console.warn(`${_e("warn(prisma)")} ${u}`)}}}}function Ki(e){if(Wl(e)){Cn(`Environment variables loaded from ${e}`);let t=An.default.config({path:e,debug:process.env.DOTENV_CONFIG_DEBUG?!0:void 0});return{dotenvResult:Hi(t),message:De(`Environment variables loaded from ${Be.default.relative(process.cwd(),e)}`),path:e}}else Cn(`Environment variables not found at ${e}`);return null}function zi(e,t){return e&&t&&Be.default.resolve(e)===Be.default.resolve(t)}function Wl(e){return!!(e&&pr.default.existsSync(e))}var Yi="library";function Qe(e){let t=Hl();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":Yi)}function Hl(){let e=process.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}var ro="prisma+postgres",dr=`${ro}:`;function Rn(e){return e?.startsWith(`${dr}//`)??!1}var Tt;(t=>{let e;(A=>(A.findUnique="findUnique",A.findUniqueOrThrow="findUniqueOrThrow",A.findFirst="findFirst",A.findFirstOrThrow="findFirstOrThrow",A.findMany="findMany",A.create="create",A.createMany="createMany",A.createManyAndReturn="createManyAndReturn",A.update="update",A.updateMany="updateMany",A.updateManyAndReturn="updateManyAndReturn",A.upsert="upsert",A.delete="delete",A.deleteMany="deleteMany",A.groupBy="groupBy",A.count="count",A.aggregate="aggregate",A.findRaw="findRaw",A.aggregateRaw="aggregateRaw"))(e=t.ModelAction||={})})(Tt||={});var Ct=B(require("path"));function Sn(e){return Ct.default.sep===Ct.default.posix.sep?e:e.split(Ct.default.sep).join(Ct.default.posix.sep)}var Rt={};yt(Rt,{error:()=>Zl,info:()=>Yl,log:()=>zl,query:()=>Xl,should:()=>oo,tags:()=>At,warn:()=>kn});var At={error:Ee("prisma:error"),warn:_e("prisma:warn"),info:be("prisma:info"),query:Ne("prisma:query")},oo={warn:()=>!process.env.PRISMA_DISABLE_WARNINGS};function zl(...e){console.log(...e)}function kn(e,...t){oo.warn()&&console.warn(`${At.warn} ${e}`,...t)}function Yl(e,...t){console.info(`${At.info} ${e}`,...t)}function Zl(e,...t){console.error(`${At.error} ${e}`,...t)}function Xl(e,...t){console.log(`${At.query} ${e}`,...t)}function oe(e,t){throw new Error(t)}function In(e,t){return Object.prototype.hasOwnProperty.call(e,t)}var Dn=(e,t)=>e.reduce((r,n)=>(r[t(n)]=n,r),{});function Ge(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function _n(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}function b(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var co=new Set,St=(e,t,...r)=>{co.has(e)||(co.add(e),kn(t,...r))};var S=class e extends Error{constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};b(S,"PrismaClientInitializationError");var Q=class extends Error{constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};b(Q,"PrismaClientKnownRequestError");var se=class extends Error{constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};b(se,"PrismaClientRustPanicError");var G=class extends Error{constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};b(G,"PrismaClientUnknownRequestError");var J=class extends Error{constructor(r,{clientVersion:n}){super(r);this.name="PrismaClientValidationError";this.clientVersion=n}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};b(J,"PrismaClientValidationError");var Je=9e15,Se=1e9,Nn="0123456789abcdef",hr="2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058",yr="3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789",Mn={precision:20,rounding:4,modulo:1,toExpNeg:-7,toExpPos:21,minE:-Je,maxE:Je,crypto:!1},ho,ve,E=!0,Er="[DecimalError] ",Re=Er+"Invalid argument: ",yo=Er+"Precision limit exceeded",wo=Er+"crypto unavailable",Eo="[object Decimal]",W=Math.floor,$=Math.pow,tu=/^0b([01]+(\.[01]*)?|\.[01]+)(p[+-]?\d+)?$/i,ru=/^0x([0-9a-f]+(\.[0-9a-f]*)?|\.[0-9a-f]+)(p[+-]?\d+)?$/i,nu=/^0o([0-7]+(\.[0-7]*)?|\.[0-7]+)(p[+-]?\d+)?$/i,bo=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,ae=1e7,w=7,iu=9007199254740991,ou=hr.length-1,Fn=yr.length-1,m={toStringTag:Eo};m.absoluteValue=m.abs=function(){var e=new this.constructor(this);return e.s<0&&(e.s=1),y(e)};m.ceil=function(){return y(new this.constructor(this),this.e+1,2)};m.clampedTo=m.clamp=function(e,t){var r,n=this,i=n.constructor;if(e=new i(e),t=new i(t),!e.s||!t.s)return new i(NaN);if(e.gt(t))throw Error(Re+t);return r=n.cmp(e),r<0?e:n.cmp(t)>0?t:new i(n)};m.comparedTo=m.cmp=function(e){var t,r,n,i,o=this,s=o.d,a=(e=new o.constructor(e)).d,l=o.s,u=e.s;if(!s||!a)return!l||!u?NaN:l!==u?l:s===a?0:!s^l<0?1:-1;if(!s[0]||!a[0])return s[0]?l:a[0]?-u:0;if(l!==u)return l;if(o.e!==e.e)return o.e>e.e^l<0?1:-1;for(n=s.length,i=a.length,t=0,r=n<i?n:i;t<r;++t)if(s[t]!==a[t])return s[t]>a[t]^l<0?1:-1;return n===i?0:n>i^l<0?1:-1};m.cosine=m.cos=function(){var e,t,r=this,n=r.constructor;return r.d?r.d[0]?(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+w,n.rounding=1,r=su(n,Co(n,r)),n.precision=e,n.rounding=t,y(ve==2||ve==3?r.neg():r,e,t,!0)):new n(1):new n(NaN)};m.cubeRoot=m.cbrt=function(){var e,t,r,n,i,o,s,a,l,u,c=this,p=c.constructor;if(!c.isFinite()||c.isZero())return new p(c);for(E=!1,o=c.s*$(c.s*c,1/3),!o||Math.abs(o)==1/0?(r=V(c.d),e=c.e,(o=(e-r.length+1)%3)&&(r+=o==1||o==-2?"0":"00"),o=$(r,1/3),e=W((e+1)/3)-(e%3==(e<0?-1:2)),o==1/0?r="5e"+e:(r=o.toExponential(),r=r.slice(0,r.indexOf("e")+1)+e),n=new p(r),n.s=c.s):n=new p(o.toString()),s=(e=p.precision)+3;;)if(a=n,l=a.times(a).times(a),u=l.plus(c),n=D(u.plus(c).times(a),u.plus(l),s+2,1),V(a.d).slice(0,s)===(r=V(n.d)).slice(0,s))if(r=r.slice(s-3,s+1),r=="9999"||!i&&r=="4999"){if(!i&&(y(a,e+1,0),a.times(a).times(a).eq(c))){n=a;break}s+=4,i=1}else{(!+r||!+r.slice(1)&&r.charAt(0)=="5")&&(y(n,e+1,1),t=!n.times(n).times(n).eq(c));break}return E=!0,y(n,e,p.rounding,t)};m.decimalPlaces=m.dp=function(){var e,t=this.d,r=NaN;if(t){if(e=t.length-1,r=(e-W(this.e/w))*w,e=t[e],e)for(;e%10==0;e/=10)r--;r<0&&(r=0)}return r};m.dividedBy=m.div=function(e){return D(this,new this.constructor(e))};m.dividedToIntegerBy=m.divToInt=function(e){var t=this,r=t.constructor;return y(D(t,new r(e),0,1,1),r.precision,r.rounding)};m.equals=m.eq=function(e){return this.cmp(e)===0};m.floor=function(){return y(new this.constructor(this),this.e+1,3)};m.greaterThan=m.gt=function(e){return this.cmp(e)>0};m.greaterThanOrEqualTo=m.gte=function(e){var t=this.cmp(e);return t==1||t===0};m.hyperbolicCosine=m.cosh=function(){var e,t,r,n,i,o=this,s=o.constructor,a=new s(1);if(!o.isFinite())return new s(o.s?1/0:NaN);if(o.isZero())return a;r=s.precision,n=s.rounding,s.precision=r+Math.max(o.e,o.sd())+4,s.rounding=1,i=o.d.length,i<32?(e=Math.ceil(i/3),t=(1/xr(4,e)).toString()):(e=16,t="2.3283064365386962890625e-10"),o=We(s,1,o.times(t),new s(1),!0);for(var l,u=e,c=new s(8);u--;)l=o.times(o),o=a.minus(l.times(c.minus(l.times(c))));return y(o,s.precision=r,s.rounding=n,!0)};m.hyperbolicSine=m.sinh=function(){var e,t,r,n,i=this,o=i.constructor;if(!i.isFinite()||i.isZero())return new o(i);if(t=o.precision,r=o.rounding,o.precision=t+Math.max(i.e,i.sd())+4,o.rounding=1,n=i.d.length,n<3)i=We(o,2,i,i,!0);else{e=1.4*Math.sqrt(n),e=e>16?16:e|0,i=i.times(1/xr(5,e)),i=We(o,2,i,i,!0);for(var s,a=new o(5),l=new o(16),u=new o(20);e--;)s=i.times(i),i=i.times(a.plus(s.times(l.times(s).plus(u))))}return o.precision=t,o.rounding=r,y(i,t,r,!0)};m.hyperbolicTangent=m.tanh=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+7,n.rounding=1,D(r.sinh(),r.cosh(),n.precision=e,n.rounding=t)):new n(r.s)};m.inverseCosine=m.acos=function(){var e=this,t=e.constructor,r=e.abs().cmp(1),n=t.precision,i=t.rounding;return r!==-1?r===0?e.isNeg()?pe(t,n,i):new t(0):new t(NaN):e.isZero()?pe(t,n+4,i).times(.5):(t.precision=n+6,t.rounding=1,e=new t(1).minus(e).div(e.plus(1)).sqrt().atan(),t.precision=n,t.rounding=i,e.times(2))};m.inverseHyperbolicCosine=m.acosh=function(){var e,t,r=this,n=r.constructor;return r.lte(1)?new n(r.eq(1)?0:NaN):r.isFinite()?(e=n.precision,t=n.rounding,n.precision=e+Math.max(Math.abs(r.e),r.sd())+4,n.rounding=1,E=!1,r=r.times(r).minus(1).sqrt().plus(r),E=!0,n.precision=e,n.rounding=t,r.ln()):new n(r)};m.inverseHyperbolicSine=m.asinh=function(){var e,t,r=this,n=r.constructor;return!r.isFinite()||r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+2*Math.max(Math.abs(r.e),r.sd())+6,n.rounding=1,E=!1,r=r.times(r).plus(1).sqrt().plus(r),E=!0,n.precision=e,n.rounding=t,r.ln())};m.inverseHyperbolicTangent=m.atanh=function(){var e,t,r,n,i=this,o=i.constructor;return i.isFinite()?i.e>=0?new o(i.abs().eq(1)?i.s/0:i.isZero()?i:NaN):(e=o.precision,t=o.rounding,n=i.sd(),Math.max(n,e)<2*-i.e-1?y(new o(i),e,t,!0):(o.precision=r=n-i.e,i=D(i.plus(1),new o(1).minus(i),r+e,1),o.precision=e+4,o.rounding=1,i=i.ln(),o.precision=e,o.rounding=t,i.times(.5))):new o(NaN)};m.inverseSine=m.asin=function(){var e,t,r,n,i=this,o=i.constructor;return i.isZero()?new o(i):(t=i.abs().cmp(1),r=o.precision,n=o.rounding,t!==-1?t===0?(e=pe(o,r+4,n).times(.5),e.s=i.s,e):new o(NaN):(o.precision=r+6,o.rounding=1,i=i.div(new o(1).minus(i.times(i)).sqrt().plus(1)).atan(),o.precision=r,o.rounding=n,i.times(2)))};m.inverseTangent=m.atan=function(){var e,t,r,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,d=c.rounding;if(u.isFinite()){if(u.isZero())return new c(u);if(u.abs().eq(1)&&p+4<=Fn)return s=pe(c,p+4,d).times(.25),s.s=u.s,s}else{if(!u.s)return new c(NaN);if(p+4<=Fn)return s=pe(c,p+4,d).times(.5),s.s=u.s,s}for(c.precision=a=p+10,c.rounding=1,r=Math.min(28,a/w+2|0),e=r;e;--e)u=u.div(u.times(u).plus(1).sqrt().plus(1));for(E=!1,t=Math.ceil(a/w),n=1,l=u.times(u),s=new c(u),i=u;e!==-1;)if(i=i.times(l),o=s.minus(i.div(n+=2)),i=i.times(l),s=o.plus(i.div(n+=2)),s.d[t]!==void 0)for(e=t;s.d[e]===o.d[e]&&e--;);return r&&(s=s.times(2<<r-1)),E=!0,y(s,c.precision=p,c.rounding=d,!0)};m.isFinite=function(){return!!this.d};m.isInteger=m.isInt=function(){return!!this.d&&W(this.e/w)>this.d.length-2};m.isNaN=function(){return!this.s};m.isNegative=m.isNeg=function(){return this.s<0};m.isPositive=m.isPos=function(){return this.s>0};m.isZero=function(){return!!this.d&&this.d[0]===0};m.lessThan=m.lt=function(e){return this.cmp(e)<0};m.lessThanOrEqualTo=m.lte=function(e){return this.cmp(e)<1};m.logarithm=m.log=function(e){var t,r,n,i,o,s,a,l,u=this,c=u.constructor,p=c.precision,d=c.rounding,f=5;if(e==null)e=new c(10),t=!0;else{if(e=new c(e),r=e.d,e.s<0||!r||!r[0]||e.eq(1))return new c(NaN);t=e.eq(10)}if(r=u.d,u.s<0||!r||!r[0]||u.eq(1))return new c(r&&!r[0]?-1/0:u.s!=1?NaN:r?0:1/0);if(t)if(r.length>1)o=!0;else{for(i=r[0];i%10===0;)i/=10;o=i!==1}if(E=!1,a=p+f,s=Ae(u,a),n=t?wr(c,a+10):Ae(e,a),l=D(s,n,a,1),kt(l.d,i=p,d))do if(a+=10,s=Ae(u,a),n=t?wr(c,a+10):Ae(e,a),l=D(s,n,a,1),!o){+V(l.d).slice(i+1,i+15)+1==1e14&&(l=y(l,p+1,0));break}while(kt(l.d,i+=10,d));return E=!0,y(l,p,d)};m.minus=m.sub=function(e){var t,r,n,i,o,s,a,l,u,c,p,d,f=this,g=f.constructor;if(e=new g(e),!f.d||!e.d)return!f.s||!e.s?e=new g(NaN):f.d?e.s=-e.s:e=new g(e.d||f.s!==e.s?f:NaN),e;if(f.s!=e.s)return e.s=-e.s,f.plus(e);if(u=f.d,d=e.d,a=g.precision,l=g.rounding,!u[0]||!d[0]){if(d[0])e.s=-e.s;else if(u[0])e=new g(f);else return new g(l===3?-0:0);return E?y(e,a,l):e}if(r=W(e.e/w),c=W(f.e/w),u=u.slice(),o=c-r,o){for(p=o<0,p?(t=u,o=-o,s=d.length):(t=d,r=c,s=u.length),n=Math.max(Math.ceil(a/w),s)+2,o>n&&(o=n,t.length=1),t.reverse(),n=o;n--;)t.push(0);t.reverse()}else{for(n=u.length,s=d.length,p=n<s,p&&(s=n),n=0;n<s;n++)if(u[n]!=d[n]){p=u[n]<d[n];break}o=0}for(p&&(t=u,u=d,d=t,e.s=-e.s),s=u.length,n=d.length-s;n>0;--n)u[s++]=0;for(n=d.length;n>o;){if(u[--n]<d[n]){for(i=n;i&&u[--i]===0;)u[i]=ae-1;--u[i],u[n]+=ae}u[n]-=d[n]}for(;u[--s]===0;)u.pop();for(;u[0]===0;u.shift())--r;return u[0]?(e.d=u,e.e=br(u,r),E?y(e,a,l):e):new g(l===3?-0:0)};m.modulo=m.mod=function(e){var t,r=this,n=r.constructor;return e=new n(e),!r.d||!e.s||e.d&&!e.d[0]?new n(NaN):!e.d||r.d&&!r.d[0]?y(new n(r),n.precision,n.rounding):(E=!1,n.modulo==9?(t=D(r,e.abs(),0,3,1),t.s*=e.s):t=D(r,e,0,n.modulo,1),t=t.times(e),E=!0,r.minus(t))};m.naturalExponential=m.exp=function(){return Ln(this)};m.naturalLogarithm=m.ln=function(){return Ae(this)};m.negated=m.neg=function(){var e=new this.constructor(this);return e.s=-e.s,y(e)};m.plus=m.add=function(e){var t,r,n,i,o,s,a,l,u,c,p=this,d=p.constructor;if(e=new d(e),!p.d||!e.d)return!p.s||!e.s?e=new d(NaN):p.d||(e=new d(e.d||p.s===e.s?p:NaN)),e;if(p.s!=e.s)return e.s=-e.s,p.minus(e);if(u=p.d,c=e.d,a=d.precision,l=d.rounding,!u[0]||!c[0])return c[0]||(e=new d(p)),E?y(e,a,l):e;if(o=W(p.e/w),n=W(e.e/w),u=u.slice(),i=o-n,i){for(i<0?(r=u,i=-i,s=c.length):(r=c,n=o,s=u.length),o=Math.ceil(a/w),s=o>s?o+1:s+1,i>s&&(i=s,r.length=1),r.reverse();i--;)r.push(0);r.reverse()}for(s=u.length,i=c.length,s-i<0&&(i=s,r=c,c=u,u=r),t=0;i;)t=(u[--i]=u[i]+c[i]+t)/ae|0,u[i]%=ae;for(t&&(u.unshift(t),++n),s=u.length;u[--s]==0;)u.pop();return e.d=u,e.e=br(u,n),E?y(e,a,l):e};m.precision=m.sd=function(e){var t,r=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(Re+e);return r.d?(t=xo(r.d),e&&r.e+1>t&&(t=r.e+1)):t=NaN,t};m.round=function(){var e=this,t=e.constructor;return y(new t(e),e.e+1,t.rounding)};m.sine=m.sin=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+Math.max(r.e,r.sd())+w,n.rounding=1,r=lu(n,Co(n,r)),n.precision=e,n.rounding=t,y(ve>2?r.neg():r,e,t,!0)):new n(NaN)};m.squareRoot=m.sqrt=function(){var e,t,r,n,i,o,s=this,a=s.d,l=s.e,u=s.s,c=s.constructor;if(u!==1||!a||!a[0])return new c(!u||u<0&&(!a||a[0])?NaN:a?s:1/0);for(E=!1,u=Math.sqrt(+s),u==0||u==1/0?(t=V(a),(t.length+l)%2==0&&(t+="0"),u=Math.sqrt(t),l=W((l+1)/2)-(l<0||l%2),u==1/0?t="5e"+l:(t=u.toExponential(),t=t.slice(0,t.indexOf("e")+1)+l),n=new c(t)):n=new c(u.toString()),r=(l=c.precision)+3;;)if(o=n,n=o.plus(D(s,o,r+2,1)).times(.5),V(o.d).slice(0,r)===(t=V(n.d)).slice(0,r))if(t=t.slice(r-3,r+1),t=="9999"||!i&&t=="4999"){if(!i&&(y(o,l+1,0),o.times(o).eq(s))){n=o;break}r+=4,i=1}else{(!+t||!+t.slice(1)&&t.charAt(0)=="5")&&(y(n,l+1,1),e=!n.times(n).eq(s));break}return E=!0,y(n,l,c.rounding,e)};m.tangent=m.tan=function(){var e,t,r=this,n=r.constructor;return r.isFinite()?r.isZero()?new n(r):(e=n.precision,t=n.rounding,n.precision=e+10,n.rounding=1,r=r.sin(),r.s=1,r=D(r,new n(1).minus(r.times(r)).sqrt(),e+10,0),n.precision=e,n.rounding=t,y(ve==2||ve==4?r.neg():r,e,t,!0)):new n(NaN)};m.times=m.mul=function(e){var t,r,n,i,o,s,a,l,u,c=this,p=c.constructor,d=c.d,f=(e=new p(e)).d;if(e.s*=c.s,!d||!d[0]||!f||!f[0])return new p(!e.s||d&&!d[0]&&!f||f&&!f[0]&&!d?NaN:!d||!f?e.s/0:e.s*0);for(r=W(c.e/w)+W(e.e/w),l=d.length,u=f.length,l<u&&(o=d,d=f,f=o,s=l,l=u,u=s),o=[],s=l+u,n=s;n--;)o.push(0);for(n=u;--n>=0;){for(t=0,i=l+n;i>n;)a=o[i]+f[n]*d[i-n-1]+t,o[i--]=a%ae|0,t=a/ae|0;o[i]=(o[i]+t)%ae|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=br(o,r),E?y(e,p.precision,p.rounding):e};m.toBinary=function(e,t){return $n(this,2,e,t)};m.toDecimalPlaces=m.toDP=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(Z(e,0,Se),t===void 0?t=n.rounding:Z(t,0,8),y(r,e+r.e+1,t))};m.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=de(n,!0):(Z(e,0,Se),t===void 0?t=i.rounding:Z(t,0,8),n=y(new i(n),e+1,t),r=de(n,!0,e+1)),n.isNeg()&&!n.isZero()?"-"+r:r};m.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?r=de(i):(Z(e,0,Se),t===void 0?t=o.rounding:Z(t,0,8),n=y(new o(i),e+i.e+1,t),r=de(n,!1,e+n.e+1)),i.isNeg()&&!i.isZero()?"-"+r:r};m.toFraction=function(e){var t,r,n,i,o,s,a,l,u,c,p,d,f=this,g=f.d,h=f.constructor;if(!g)return new h(f);if(u=r=new h(1),n=l=new h(0),t=new h(n),o=t.e=xo(g)-f.e-1,s=o%w,t.d[0]=$(10,s<0?w+s:s),e==null)e=o>0?t:u;else{if(a=new h(e),!a.isInt()||a.lt(u))throw Error(Re+a);e=a.gt(t)?o>0?t:u:a}for(E=!1,a=new h(V(g)),c=h.precision,h.precision=o=g.length*w*2;p=D(a,t,0,1,1),i=r.plus(p.times(n)),i.cmp(e)!=1;)r=n,n=i,i=u,u=l.plus(p.times(i)),l=i,i=t,t=a.minus(p.times(i)),a=i;return i=D(e.minus(r),n,0,1,1),l=l.plus(i.times(u)),r=r.plus(i.times(n)),l.s=u.s=f.s,d=D(u,n,o,1).minus(f).abs().cmp(D(l,r,o,1).minus(f).abs())<1?[u,n]:[l,r],h.precision=c,E=!0,d};m.toHexadecimal=m.toHex=function(e,t){return $n(this,16,e,t)};m.toNearest=function(e,t){var r=this,n=r.constructor;if(r=new n(r),e==null){if(!r.d)return r;e=new n(1),t=n.rounding}else{if(e=new n(e),t===void 0?t=n.rounding:Z(t,0,8),!r.d)return e.s?r:e;if(!e.d)return e.s&&(e.s=r.s),e}return e.d[0]?(E=!1,r=D(r,e,0,t,1).times(e),E=!0,y(r)):(e.s=r.s,r=e),r};m.toNumber=function(){return+this};m.toOctal=function(e,t){return $n(this,8,e,t)};m.toPower=m.pow=function(e){var t,r,n,i,o,s,a=this,l=a.constructor,u=+(e=new l(e));if(!a.d||!e.d||!a.d[0]||!e.d[0])return new l($(+a,u));if(a=new l(a),a.eq(1))return a;if(n=l.precision,o=l.rounding,e.eq(1))return y(a,n,o);if(t=W(e.e/w),t>=e.d.length-1&&(r=u<0?-u:u)<=iu)return i=vo(l,a,r,n),e.s<0?new l(1).div(i):y(i,n,o);if(s=a.s,s<0){if(t<e.d.length-1)return new l(NaN);if(e.d[t]&1||(s=1),a.e==0&&a.d[0]==1&&a.d.length==1)return a.s=s,a}return r=$(+a,u),t=r==0||!isFinite(r)?W(u*(Math.log("0."+V(a.d))/Math.LN10+a.e+1)):new l(r+"").e,t>l.maxE+1||t<l.minE-1?new l(t>0?s/0:0):(E=!1,l.rounding=a.s=1,r=Math.min(12,(t+"").length),i=Ln(e.times(Ae(a,n+r)),n),i.d&&(i=y(i,n+5,1),kt(i.d,n,o)&&(t=n+10,i=y(Ln(e.times(Ae(a,t+r)),t),t+5,1),+V(i.d).slice(n+1,n+15)+1==1e14&&(i=y(i,n+1,0)))),i.s=s,E=!0,l.rounding=o,y(i,n,o))};m.toPrecision=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=de(n,n.e<=i.toExpNeg||n.e>=i.toExpPos):(Z(e,1,Se),t===void 0?t=i.rounding:Z(t,0,8),n=y(new i(n),e,t),r=de(n,e<=n.e||n.e<=i.toExpNeg,e)),n.isNeg()&&!n.isZero()?"-"+r:r};m.toSignificantDigits=m.toSD=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(Z(e,1,Se),t===void 0?t=n.rounding:Z(t,0,8)),y(new n(r),e,t)};m.toString=function(){var e=this,t=e.constructor,r=de(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()&&!e.isZero()?"-"+r:r};m.truncated=m.trunc=function(){return y(new this.constructor(this),this.e+1,1)};m.valueOf=m.toJSON=function(){var e=this,t=e.constructor,r=de(e,e.e<=t.toExpNeg||e.e>=t.toExpPos);return e.isNeg()?"-"+r:r};function V(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=w-n.length,r&&(o+=Ce(r)),o+=n;s=e[t],n=s+"",r=w-n.length,r&&(o+=Ce(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function Z(e,t,r){if(e!==~~e||e<t||e>r)throw Error(Re+e)}function kt(e,t,r,n){var i,o,s,a;for(o=e[0];o>=10;o/=10)--t;return--t<0?(t+=w,i=0):(i=Math.ceil((t+1)/w),t%=w),o=$(10,w-t),a=e[i]%o|0,n==null?t<3?(t==0?a=a/100|0:t==1&&(a=a/10|0),s=r<4&&a==99999||r>3&&a==49999||a==5e4||a==0):s=(r<4&&a+1==o||r>3&&a+1==o/2)&&(e[i+1]/o/100|0)==$(10,t-2)-1||(a==o/2||a==0)&&(e[i+1]/o/100|0)==0:t<4?(t==0?a=a/1e3|0:t==1?a=a/100|0:t==2&&(a=a/10|0),s=(n||r<4)&&a==9999||!n&&r>3&&a==4999):s=((n||r<4)&&a+1==o||!n&&r>3&&a+1==o/2)&&(e[i+1]/o/1e3|0)==$(10,t-3)-1,s}function fr(e,t,r){for(var n,i=[0],o,s=0,a=e.length;s<a;){for(o=i.length;o--;)i[o]*=t;for(i[0]+=Nn.indexOf(e.charAt(s++)),n=0;n<i.length;n++)i[n]>r-1&&(i[n+1]===void 0&&(i[n+1]=0),i[n+1]+=i[n]/r|0,i[n]%=r)}return i.reverse()}function su(e,t){var r,n,i;if(t.isZero())return t;n=t.d.length,n<32?(r=Math.ceil(n/3),i=(1/xr(4,r)).toString()):(r=16,i="2.3283064365386962890625e-10"),e.precision+=r,t=We(e,1,t.times(i),new e(1));for(var o=r;o--;){var s=t.times(t);t=s.times(s).minus(s).times(8).plus(1)}return e.precision-=r,t}var D=function(){function e(n,i,o){var s,a=0,l=n.length;for(n=n.slice();l--;)s=n[l]*i+a,n[l]=s%o|0,a=s/o|0;return a&&n.unshift(a),n}function t(n,i,o,s){var a,l;if(o!=s)l=o>s?1:-1;else for(a=l=0;a<o;a++)if(n[a]!=i[a]){l=n[a]>i[a]?1:-1;break}return l}function r(n,i,o,s){for(var a=0;o--;)n[o]-=a,a=n[o]<i[o]?1:0,n[o]=a*s+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s,a,l){var u,c,p,d,f,g,h,R,v,T,P,k,A,te,ht,F,z,we,j,Ue,ar=n.constructor,dn=n.s==i.s?1:-1,U=n.d,I=i.d;if(!U||!U[0]||!I||!I[0])return new ar(!n.s||!i.s||(U?I&&U[0]==I[0]:!I)?NaN:U&&U[0]==0||!I?dn*0:dn/0);for(l?(f=1,c=n.e-i.e):(l=ae,f=w,c=W(n.e/f)-W(i.e/f)),j=I.length,z=U.length,v=new ar(dn),T=v.d=[],p=0;I[p]==(U[p]||0);p++);if(I[p]>(U[p]||0)&&c--,o==null?(te=o=ar.precision,s=ar.rounding):a?te=o+(n.e-i.e)+1:te=o,te<0)T.push(1),g=!0;else{if(te=te/f+2|0,p=0,j==1){for(d=0,I=I[0],te++;(p<z||d)&&te--;p++)ht=d*l+(U[p]||0),T[p]=ht/I|0,d=ht%I|0;g=d||p<z}else{for(d=l/(I[0]+1)|0,d>1&&(I=e(I,d,l),U=e(U,d,l),j=I.length,z=U.length),F=j,P=U.slice(0,j),k=P.length;k<j;)P[k++]=0;Ue=I.slice(),Ue.unshift(0),we=I[0],I[1]>=l/2&&++we;do d=0,u=t(I,P,j,k),u<0?(A=P[0],j!=k&&(A=A*l+(P[1]||0)),d=A/we|0,d>1?(d>=l&&(d=l-1),h=e(I,d,l),R=h.length,k=P.length,u=t(h,P,R,k),u==1&&(d--,r(h,j<R?Ue:I,R,l))):(d==0&&(u=d=1),h=I.slice()),R=h.length,R<k&&h.unshift(0),r(P,h,k,l),u==-1&&(k=P.length,u=t(I,P,j,k),u<1&&(d++,r(P,j<k?Ue:I,k,l))),k=P.length):u===0&&(d++,P=[0]),T[p++]=d,u&&P[0]?P[k++]=U[F]||0:(P=[U[F]],k=1);while((F++<z||P[0]!==void 0)&&te--);g=P[0]!==void 0}T[0]||T.shift()}if(f==1)v.e=c,ho=g;else{for(p=1,d=T[0];d>=10;d/=10)p++;v.e=p+c*f-1,y(v,a?o+v.e+1:o,s,g)}return v}}();function y(e,t,r,n){var i,o,s,a,l,u,c,p,d,f=e.constructor;e:if(t!=null){if(p=e.d,!p)return e;for(i=1,a=p[0];a>=10;a/=10)i++;if(o=t-i,o<0)o+=w,s=t,c=p[d=0],l=c/$(10,i-s-1)%10|0;else if(d=Math.ceil((o+1)/w),a=p.length,d>=a)if(n){for(;a++<=d;)p.push(0);c=l=0,i=1,o%=w,s=o-w+1}else break e;else{for(c=a=p[d],i=1;a>=10;a/=10)i++;o%=w,s=o-w+i,l=s<0?0:c/$(10,i-s-1)%10|0}if(n=n||t<0||p[d+1]!==void 0||(s<0?c:c%$(10,i-s-1)),u=r<4?(l||n)&&(r==0||r==(e.s<0?3:2)):l>5||l==5&&(r==4||n||r==6&&(o>0?s>0?c/$(10,i-s):0:p[d-1])%10&1||r==(e.s<0?8:7)),t<1||!p[0])return p.length=0,u?(t-=e.e+1,p[0]=$(10,(w-t%w)%w),e.e=-t||0):p[0]=e.e=0,e;if(o==0?(p.length=d,a=1,d--):(p.length=d+1,a=$(10,w-o),p[d]=s>0?(c/$(10,i-s)%$(10,s)|0)*a:0),u)for(;;)if(d==0){for(o=1,s=p[0];s>=10;s/=10)o++;for(s=p[0]+=a,a=1;s>=10;s/=10)a++;o!=a&&(e.e++,p[0]==ae&&(p[0]=1));break}else{if(p[d]+=a,p[d]!=ae)break;p[d--]=0,a=1}for(o=p.length;p[--o]===0;)p.pop()}return E&&(e.e>f.maxE?(e.d=null,e.e=NaN):e.e<f.minE&&(e.e=0,e.d=[0])),e}function de(e,t,r){if(!e.isFinite())return To(e);var n,i=e.e,o=V(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Ce(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(e.e<0?"e":"e+")+e.e):i<0?(o="0."+Ce(-i-1)+o,r&&(n=r-s)>0&&(o+=Ce(n))):i>=s?(o+=Ce(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Ce(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Ce(n))),o}function br(e,t){var r=e[0];for(t*=w;r>=10;r/=10)t++;return t}function wr(e,t,r){if(t>ou)throw E=!0,r&&(e.precision=r),Error(yo);return y(new e(hr),t,1,!0)}function pe(e,t,r){if(t>Fn)throw Error(yo);return y(new e(yr),t,r,!0)}function xo(e){var t=e.length-1,r=t*w+1;if(t=e[t],t){for(;t%10==0;t/=10)r--;for(t=e[0];t>=10;t/=10)r++}return r}function Ce(e){for(var t="";e--;)t+="0";return t}function vo(e,t,r,n){var i,o=new e(1),s=Math.ceil(n/w+4);for(E=!1;;){if(r%2&&(o=o.times(t),fo(o.d,s)&&(i=!0)),r=W(r/2),r===0){r=o.d.length-1,i&&o.d[r]===0&&++o.d[r];break}t=t.times(t),fo(t.d,s)}return E=!0,o}function mo(e){return e.d[e.d.length-1]&1}function Po(e,t,r){for(var n,i,o=new e(t[0]),s=0;++s<t.length;){if(i=new e(t[s]),!i.s){o=i;break}n=o.cmp(i),(n===r||n===0&&o.s===r)&&(o=i)}return o}function Ln(e,t){var r,n,i,o,s,a,l,u=0,c=0,p=0,d=e.constructor,f=d.rounding,g=d.precision;if(!e.d||!e.d[0]||e.e>17)return new d(e.d?e.d[0]?e.s<0?0:1/0:1:e.s?e.s<0?0:e:NaN);for(t==null?(E=!1,l=g):l=t,a=new d(.03125);e.e>-2;)e=e.times(a),p+=5;for(n=Math.log($(2,p))/Math.LN10*2+5|0,l+=n,r=o=s=new d(1),d.precision=l;;){if(o=y(o.times(e),l,1),r=r.times(++c),a=s.plus(D(o,r,l,1)),V(a.d).slice(0,l)===V(s.d).slice(0,l)){for(i=p;i--;)s=y(s.times(s),l,1);if(t==null)if(u<3&&kt(s.d,l-n,f,u))d.precision=l+=10,r=o=a=new d(1),c=0,u++;else return y(s,d.precision=g,f,E=!0);else return d.precision=g,s}s=a}}function Ae(e,t){var r,n,i,o,s,a,l,u,c,p,d,f=1,g=10,h=e,R=h.d,v=h.constructor,T=v.rounding,P=v.precision;if(h.s<0||!R||!R[0]||!h.e&&R[0]==1&&R.length==1)return new v(R&&!R[0]?-1/0:h.s!=1?NaN:R?0:h);if(t==null?(E=!1,c=P):c=t,v.precision=c+=g,r=V(R),n=r.charAt(0),Math.abs(o=h.e)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)h=h.times(e),r=V(h.d),n=r.charAt(0),f++;o=h.e,n>1?(h=new v("0."+r),o++):h=new v(n+"."+r.slice(1))}else return u=wr(v,c+2,P).times(o+""),h=Ae(new v(n+"."+r.slice(1)),c-g).plus(u),v.precision=P,t==null?y(h,P,T,E=!0):h;for(p=h,l=s=h=D(h.minus(1),h.plus(1),c,1),d=y(h.times(h),c,1),i=3;;){if(s=y(s.times(d),c,1),u=l.plus(D(s,new v(i),c,1)),V(u.d).slice(0,c)===V(l.d).slice(0,c))if(l=l.times(2),o!==0&&(l=l.plus(wr(v,c+2,P).times(o+""))),l=D(l,new v(f),c,1),t==null)if(kt(l.d,c-g,T,a))v.precision=c+=g,u=s=h=D(p.minus(1),p.plus(1),c,1),d=y(h.times(h),c,1),i=a=1;else return y(l,v.precision=P,T,E=!0);else return v.precision=P,l;l=u,i+=2}}function To(e){return String(e.s*e.s/0)}function gr(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;n++);for(i=t.length;t.charCodeAt(i-1)===48;--i);if(t=t.slice(n,i),t){if(i-=n,e.e=r=r-n-1,e.d=[],n=(r+1)%w,r<0&&(n+=w),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=w;n<i;)e.d.push(+t.slice(n,n+=w));t=t.slice(n),n=w-t.length}else n-=i;for(;n--;)t+="0";e.d.push(+t),E&&(e.e>e.constructor.maxE?(e.d=null,e.e=NaN):e.e<e.constructor.minE&&(e.e=0,e.d=[0]))}else e.e=0,e.d=[0];return e}function au(e,t){var r,n,i,o,s,a,l,u,c;if(t.indexOf("_")>-1){if(t=t.replace(/(\d)_(?=\d)/g,"$1"),bo.test(t))return gr(e,t)}else if(t==="Infinity"||t==="NaN")return+t||(e.s=NaN),e.e=NaN,e.d=null,e;if(ru.test(t))r=16,t=t.toLowerCase();else if(tu.test(t))r=2;else if(nu.test(t))r=8;else throw Error(Re+t);for(o=t.search(/p/i),o>0?(l=+t.slice(o+1),t=t.substring(2,o)):t=t.slice(2),o=t.indexOf("."),s=o>=0,n=e.constructor,s&&(t=t.replace(".",""),a=t.length,o=a-o,i=vo(n,new n(r),o,o*2)),u=fr(t,r,ae),c=u.length-1,o=c;u[o]===0;--o)u.pop();return o<0?new n(e.s*0):(e.e=br(u,c),e.d=u,E=!1,s&&(e=D(e,i,a*4)),l&&(e=e.times(Math.abs(l)<54?$(2,l):Me.pow(2,l))),E=!0,e)}function lu(e,t){var r,n=t.d.length;if(n<3)return t.isZero()?t:We(e,2,t,t);r=1.4*Math.sqrt(n),r=r>16?16:r|0,t=t.times(1/xr(5,r)),t=We(e,2,t,t);for(var i,o=new e(5),s=new e(16),a=new e(20);r--;)i=t.times(t),t=t.times(o.plus(i.times(s.times(i).minus(a))));return t}function We(e,t,r,n,i){var o,s,a,l,u=1,c=e.precision,p=Math.ceil(c/w);for(E=!1,l=r.times(r),a=new e(n);;){if(s=D(a.times(l),new e(t++*t++),c,1),a=i?n.plus(s):n.minus(s),n=D(s.times(l),new e(t++*t++),c,1),s=a.plus(n),s.d[p]!==void 0){for(o=p;s.d[o]===a.d[o]&&o--;);if(o==-1)break}o=a,a=n,n=s,s=o,u++}return E=!0,s.d.length=p+1,s}function xr(e,t){for(var r=e;--t;)r*=e;return r}function Co(e,t){var r,n=t.s<0,i=pe(e,e.precision,1),o=i.times(.5);if(t=t.abs(),t.lte(o))return ve=n?4:1,t;if(r=t.divToInt(i),r.isZero())ve=n?3:2;else{if(t=t.minus(r.times(i)),t.lte(o))return ve=mo(r)?n?2:3:n?4:1,t;ve=mo(r)?n?1:4:n?3:2}return t.minus(i).abs()}function $n(e,t,r,n){var i,o,s,a,l,u,c,p,d,f=e.constructor,g=r!==void 0;if(g?(Z(r,1,Se),n===void 0?n=f.rounding:Z(n,0,8)):(r=f.precision,n=f.rounding),!e.isFinite())c=To(e);else{for(c=de(e),s=c.indexOf("."),g?(i=2,t==16?r=r*4-3:t==8&&(r=r*3-2)):i=t,s>=0&&(c=c.replace(".",""),d=new f(1),d.e=c.length-s,d.d=fr(de(d),10,i),d.e=d.d.length),p=fr(c,10,i),o=l=p.length;p[--l]==0;)p.pop();if(!p[0])c=g?"0p+0":"0";else{if(s<0?o--:(e=new f(e),e.d=p,e.e=o,e=D(e,d,r,n,0,i),p=e.d,o=e.e,u=ho),s=p[r],a=i/2,u=u||p[r+1]!==void 0,u=n<4?(s!==void 0||u)&&(n===0||n===(e.s<0?3:2)):s>a||s===a&&(n===4||u||n===6&&p[r-1]&1||n===(e.s<0?8:7)),p.length=r,u)for(;++p[--r]>i-1;)p[r]=0,r||(++o,p.unshift(1));for(l=p.length;!p[l-1];--l);for(s=0,c="";s<l;s++)c+=Nn.charAt(p[s]);if(g){if(l>1)if(t==16||t==8){for(s=t==16?4:3,--l;l%s;l++)c+="0";for(p=fr(c,i,t),l=p.length;!p[l-1];--l);for(s=1,c="1.";s<l;s++)c+=Nn.charAt(p[s])}else c=c.charAt(0)+"."+c.slice(1);c=c+(o<0?"p":"p+")+o}else if(o<0){for(;++o;)c="0"+c;c="0."+c}else if(++o>l)for(o-=l;o--;)c+="0";else o<l&&(c=c.slice(0,o)+"."+c.slice(o))}c=(t==16?"0x":t==2?"0b":t==8?"0o":"")+c}return e.s<0?"-"+c:c}function fo(e,t){if(e.length>t)return e.length=t,!0}function uu(e){return new this(e).abs()}function cu(e){return new this(e).acos()}function pu(e){return new this(e).acosh()}function du(e,t){return new this(e).plus(t)}function mu(e){return new this(e).asin()}function fu(e){return new this(e).asinh()}function gu(e){return new this(e).atan()}function hu(e){return new this(e).atanh()}function yu(e,t){e=new this(e),t=new this(t);var r,n=this.precision,i=this.rounding,o=n+4;return!e.s||!t.s?r=new this(NaN):!e.d&&!t.d?(r=pe(this,o,1).times(t.s>0?.25:.75),r.s=e.s):!t.d||e.isZero()?(r=t.s<0?pe(this,n,i):new this(0),r.s=e.s):!e.d||t.isZero()?(r=pe(this,o,1).times(.5),r.s=e.s):t.s<0?(this.precision=o,this.rounding=1,r=this.atan(D(e,t,o,1)),t=pe(this,o,1),this.precision=n,this.rounding=i,r=e.s<0?r.minus(t):r.plus(t)):r=this.atan(D(e,t,o,1)),r}function wu(e){return new this(e).cbrt()}function Eu(e){return y(e=new this(e),e.e+1,2)}function bu(e,t,r){return new this(e).clamp(t,r)}function xu(e){if(!e||typeof e!="object")throw Error(Er+"Object expected");var t,r,n,i=e.defaults===!0,o=["precision",1,Se,"rounding",0,8,"toExpNeg",-Je,0,"toExpPos",0,Je,"maxE",0,Je,"minE",-Je,0,"modulo",0,9];for(t=0;t<o.length;t+=3)if(r=o[t],i&&(this[r]=Mn[r]),(n=e[r])!==void 0)if(W(n)===n&&n>=o[t+1]&&n<=o[t+2])this[r]=n;else throw Error(Re+r+": "+n);if(r="crypto",i&&(this[r]=Mn[r]),(n=e[r])!==void 0)if(n===!0||n===!1||n===0||n===1)if(n)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))this[r]=!0;else throw Error(wo);else this[r]=!1;else throw Error(Re+r+": "+n);return this}function vu(e){return new this(e).cos()}function Pu(e){return new this(e).cosh()}function Ao(e){var t,r,n;function i(o){var s,a,l,u=this;if(!(u instanceof i))return new i(o);if(u.constructor=i,go(o)){u.s=o.s,E?!o.d||o.e>i.maxE?(u.e=NaN,u.d=null):o.e<i.minE?(u.e=0,u.d=[0]):(u.e=o.e,u.d=o.d.slice()):(u.e=o.e,u.d=o.d?o.d.slice():o.d);return}if(l=typeof o,l==="number"){if(o===0){u.s=1/o<0?-1:1,u.e=0,u.d=[0];return}if(o<0?(o=-o,u.s=-1):u.s=1,o===~~o&&o<1e7){for(s=0,a=o;a>=10;a/=10)s++;E?s>i.maxE?(u.e=NaN,u.d=null):s<i.minE?(u.e=0,u.d=[0]):(u.e=s,u.d=[o]):(u.e=s,u.d=[o]);return}if(o*0!==0){o||(u.s=NaN),u.e=NaN,u.d=null;return}return gr(u,o.toString())}if(l==="string")return(a=o.charCodeAt(0))===45?(o=o.slice(1),u.s=-1):(a===43&&(o=o.slice(1)),u.s=1),bo.test(o)?gr(u,o):au(u,o);if(l==="bigint")return o<0?(o=-o,u.s=-1):u.s=1,gr(u,o.toString());throw Error(Re+o)}if(i.prototype=m,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.EUCLID=9,i.config=i.set=xu,i.clone=Ao,i.isDecimal=go,i.abs=uu,i.acos=cu,i.acosh=pu,i.add=du,i.asin=mu,i.asinh=fu,i.atan=gu,i.atanh=hu,i.atan2=yu,i.cbrt=wu,i.ceil=Eu,i.clamp=bu,i.cos=vu,i.cosh=Pu,i.div=Tu,i.exp=Cu,i.floor=Au,i.hypot=Ru,i.ln=Su,i.log=ku,i.log10=Iu,i.log2=Ou,i.max=Du,i.min=_u,i.mod=Nu,i.mul=Mu,i.pow=Fu,i.random=Lu,i.round=$u,i.sign=qu,i.sin=Vu,i.sinh=ju,i.sqrt=Uu,i.sub=Bu,i.sum=Qu,i.tan=Gu,i.tanh=Ju,i.trunc=Wu,e===void 0&&(e={}),e&&e.defaults!==!0)for(n=["precision","rounding","toExpNeg","toExpPos","maxE","minE","modulo","crypto"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function Tu(e,t){return new this(e).div(t)}function Cu(e){return new this(e).exp()}function Au(e){return y(e=new this(e),e.e+1,3)}function Ru(){var e,t,r=new this(0);for(E=!1,e=0;e<arguments.length;)if(t=new this(arguments[e++]),t.d)r.d&&(r=r.plus(t.times(t)));else{if(t.s)return E=!0,new this(1/0);r=t}return E=!0,r.sqrt()}function go(e){return e instanceof Me||e&&e.toStringTag===Eo||!1}function Su(e){return new this(e).ln()}function ku(e,t){return new this(e).log(t)}function Ou(e){return new this(e).log(2)}function Iu(e){return new this(e).log(10)}function Du(){return Po(this,arguments,-1)}function _u(){return Po(this,arguments,1)}function Nu(e,t){return new this(e).mod(t)}function Mu(e,t){return new this(e).mul(t)}function Fu(e,t){return new this(e).pow(t)}function Lu(e){var t,r,n,i,o=0,s=new this(1),a=[];if(e===void 0?e=this.precision:Z(e,1,Se),n=Math.ceil(e/w),this.crypto)if(crypto.getRandomValues)for(t=crypto.getRandomValues(new Uint32Array(n));o<n;)i=t[o],i>=429e7?t[o]=crypto.getRandomValues(new Uint32Array(1))[0]:a[o++]=i%1e7;else if(crypto.randomBytes){for(t=crypto.randomBytes(n*=4);o<n;)i=t[o]+(t[o+1]<<8)+(t[o+2]<<16)+((t[o+3]&127)<<24),i>=214e7?crypto.randomBytes(4).copy(t,o):(a.push(i%1e7),o+=4);o=n/4}else throw Error(wo);else for(;o<n;)a[o++]=Math.random()*1e7|0;for(n=a[--o],e%=w,n&&e&&(i=$(10,w-e),a[o]=(n/i|0)*i);a[o]===0;o--)a.pop();if(o<0)r=0,a=[0];else{for(r=-1;a[0]===0;r-=w)a.shift();for(n=1,i=a[0];i>=10;i/=10)n++;n<w&&(r-=w-n)}return s.e=r,s.d=a,s}function $u(e){return y(e=new this(e),e.e+1,this.rounding)}function qu(e){return e=new this(e),e.d?e.d[0]?e.s:0*e.s:e.s||NaN}function Vu(e){return new this(e).sin()}function ju(e){return new this(e).sinh()}function Uu(e){return new this(e).sqrt()}function Bu(e,t){return new this(e).sub(t)}function Qu(){var e=0,t=arguments,r=new this(t[e]);for(E=!1;r.s&&++e<t.length;)r=r.plus(t[e]);return E=!0,y(r,this.precision,this.rounding)}function Gu(e){return new this(e).tan()}function Ju(e){return new this(e).tanh()}function Wu(e){return y(e=new this(e),e.e+1,1)}m[Symbol.for("nodejs.util.inspect.custom")]=m.toString;m[Symbol.toStringTag]="Decimal";var Me=m.constructor=Ao(Mn);hr=new Me(hr);yr=new Me(yr);var me=Me;function He(e){return e===null?e:Array.isArray(e)?e.map(He):typeof e=="object"?Hu(e)?Ku(e):Ge(e,He):e}function Hu(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function Ku({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new me(t);case"Json":return JSON.parse(t);default:oe(t,"Unknown tagged value")}}function Ke(e){return e.substring(0,1).toLowerCase()+e.substring(1)}function ze(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function vr(e){return e.toString()!=="Invalid Date"}function Ye(e){return Me.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}var Do=B(io());var Io=B(require("fs"));var Ro={keyword:be,entity:be,value:e=>H(Ne(e)),punctuation:Ne,directive:be,function:be,variable:e=>H(Ne(e)),string:e=>H(Et(e)),boolean:_e,number:be,comment:bt};var zu=e=>e,Pr={},Yu=0,x={manual:Pr.Prism&&Pr.Prism.manual,disableWorkerMessageHandler:Pr.Prism&&Pr.Prism.disableWorkerMessageHandler,util:{encode:function(e){if(e instanceof le){let t=e;return new le(t.type,x.util.encode(t.content),t.alias)}else return Array.isArray(e)?e.map(x.util.encode):e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/\u00a0/g," ")},type:function(e){return Object.prototype.toString.call(e).slice(8,-1)},objId:function(e){return e.__id||Object.defineProperty(e,"__id",{value:++Yu}),e.__id},clone:function e(t,r){let n,i,o=x.util.type(t);switch(r=r||{},o){case"Object":if(i=x.util.objId(t),r[i])return r[i];n={},r[i]=n;for(let s in t)t.hasOwnProperty(s)&&(n[s]=e(t[s],r));return n;case"Array":return i=x.util.objId(t),r[i]?r[i]:(n=[],r[i]=n,t.forEach(function(s,a){n[a]=e(s,r)}),n);default:return t}}},languages:{extend:function(e,t){let r=x.util.clone(x.languages[e]);for(let n in t)r[n]=t[n];return r},insertBefore:function(e,t,r,n){n=n||x.languages;let i=n[e],o={};for(let a in i)if(i.hasOwnProperty(a)){if(a==t)for(let l in r)r.hasOwnProperty(l)&&(o[l]=r[l]);r.hasOwnProperty(a)||(o[a]=i[a])}let s=n[e];return n[e]=o,x.languages.DFS(x.languages,function(a,l){l===s&&a!=e&&(this[a]=o)}),o},DFS:function e(t,r,n,i){i=i||{};let o=x.util.objId;for(let s in t)if(t.hasOwnProperty(s)){r.call(t,s,t[s],n||s);let a=t[s],l=x.util.type(a);l==="Object"&&!i[o(a)]?(i[o(a)]=!0,e(a,r,null,i)):l==="Array"&&!i[o(a)]&&(i[o(a)]=!0,e(a,r,s,i))}}},plugins:{},highlight:function(e,t,r){let n={code:e,grammar:t,language:r};return x.hooks.run("before-tokenize",n),n.tokens=x.tokenize(n.code,n.grammar),x.hooks.run("after-tokenize",n),le.stringify(x.util.encode(n.tokens),n.language)},matchGrammar:function(e,t,r,n,i,o,s){for(let h in r){if(!r.hasOwnProperty(h)||!r[h])continue;if(h==s)return;let R=r[h];R=x.util.type(R)==="Array"?R:[R];for(let v=0;v<R.length;++v){let T=R[v],P=T.inside,k=!!T.lookbehind,A=!!T.greedy,te=0,ht=T.alias;if(A&&!T.pattern.global){let F=T.pattern.toString().match(/[imuy]*$/)[0];T.pattern=RegExp(T.pattern.source,F+"g")}T=T.pattern||T;for(let F=n,z=i;F<t.length;z+=t[F].length,++F){let we=t[F];if(t.length>e.length)return;if(we instanceof le)continue;if(A&&F!=t.length-1){T.lastIndex=z;var p=T.exec(e);if(!p)break;var c=p.index+(k?p[1].length:0),d=p.index+p[0].length,a=F,l=z;for(let I=t.length;a<I&&(l<d||!t[a].type&&!t[a-1].greedy);++a)l+=t[a].length,c>=l&&(++F,z=l);if(t[F]instanceof le)continue;u=a-F,we=e.slice(z,l),p.index-=z}else{T.lastIndex=0;var p=T.exec(we),u=1}if(!p){if(o)break;continue}k&&(te=p[1]?p[1].length:0);var c=p.index+te,p=p[0].slice(te),d=c+p.length,f=we.slice(0,c),g=we.slice(d);let j=[F,u];f&&(++F,z+=f.length,j.push(f));let Ue=new le(h,P?x.tokenize(p,P):p,ht,p,A);if(j.push(Ue),g&&j.push(g),Array.prototype.splice.apply(t,j),u!=1&&x.matchGrammar(e,t,r,F,z,!0,h),o)break}}}},tokenize:function(e,t){let r=[e],n=t.rest;if(n){for(let i in n)t[i]=n[i];delete t.rest}return x.matchGrammar(e,r,t,0,0,!1),r},hooks:{all:{},add:function(e,t){let r=x.hooks.all;r[e]=r[e]||[],r[e].push(t)},run:function(e,t){let r=x.hooks.all[e];if(!(!r||!r.length))for(var n=0,i;i=r[n++];)i(t)}},Token:le};x.languages.clike={comment:[{pattern:/(^|[^\\])\/\*[\s\S]*?(?:\*\/|$)/,lookbehind:!0},{pattern:/(^|[^\\:])\/\/.*/,lookbehind:!0,greedy:!0}],string:{pattern:/(["'])(?:\\(?:\r\n|[\s\S])|(?!\1)[^\\\r\n])*\1/,greedy:!0},"class-name":{pattern:/((?:\b(?:class|interface|extends|implements|trait|instanceof|new)\s+)|(?:catch\s+\())[\w.\\]+/i,lookbehind:!0,inside:{punctuation:/[.\\]/}},keyword:/\b(?:if|else|while|do|for|return|in|instanceof|function|new|try|throw|catch|finally|null|break|continue)\b/,boolean:/\b(?:true|false)\b/,function:/\w+(?=\()/,number:/\b0x[\da-f]+\b|(?:\b\d+\.?\d*|\B\.\d+)(?:e[+-]?\d+)?/i,operator:/--?|\+\+?|!=?=?|<=?|>=?|==?=?|&&?|\|\|?|\?|\*|\/|~|\^|%/,punctuation:/[{}[\];(),.:]/};x.languages.javascript=x.languages.extend("clike",{"class-name":[x.languages.clike["class-name"],{pattern:/(^|[^$\w\xA0-\uFFFF])[_$A-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\.(?:prototype|constructor))/,lookbehind:!0}],keyword:[{pattern:/((?:^|})\s*)(?:catch|finally)\b/,lookbehind:!0},{pattern:/(^|[^.])\b(?:as|async(?=\s*(?:function\b|\(|[$\w\xA0-\uFFFF]|$))|await|break|case|class|const|continue|debugger|default|delete|do|else|enum|export|extends|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)\b/,lookbehind:!0}],number:/\b(?:(?:0[xX](?:[\dA-Fa-f](?:_[\dA-Fa-f])?)+|0[bB](?:[01](?:_[01])?)+|0[oO](?:[0-7](?:_[0-7])?)+)n?|(?:\d(?:_\d)?)+n|NaN|Infinity)\b|(?:\b(?:\d(?:_\d)?)+\.?(?:\d(?:_\d)?)*|\B\.(?:\d(?:_\d)?)+)(?:[Ee][+-]?(?:\d(?:_\d)?)+)?/,function:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*(?:\.\s*(?:apply|bind|call)\s*)?\()/,operator:/-[-=]?|\+[+=]?|!=?=?|<<?=?|>>?>?=?|=(?:==?|>)?|&[&=]?|\|[|=]?|\*\*?=?|\/=?|~|\^=?|%=?|\?|\.{3}/});x.languages.javascript["class-name"][0].pattern=/(\b(?:class|interface|extends|implements|instanceof|new)\s+)[\w.\\]+/;x.languages.insertBefore("javascript","keyword",{regex:{pattern:/((?:^|[^$\w\xA0-\uFFFF."'\])\s])\s*)\/(\[(?:[^\]\\\r\n]|\\.)*]|\\.|[^/\\\[\r\n])+\/[gimyus]{0,6}(?=\s*($|[\r\n,.;})\]]))/,lookbehind:!0,greedy:!0},"function-variable":{pattern:/[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*[=:]\s*(?:async\s*)?(?:\bfunction\b|(?:\((?:[^()]|\([^()]*\))*\)|[_$a-zA-Z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)\s*=>))/,alias:"function"},parameter:[{pattern:/(function(?:\s+[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*)?\s*\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\))/,lookbehind:!0,inside:x.languages.javascript},{pattern:/[_$a-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*(?=\s*=>)/i,inside:x.languages.javascript},{pattern:/(\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*=>)/,lookbehind:!0,inside:x.languages.javascript},{pattern:/((?:\b|\s|^)(?!(?:as|async|await|break|case|catch|class|const|continue|debugger|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|let|new|null|of|package|private|protected|public|return|set|static|super|switch|this|throw|try|typeof|undefined|var|void|while|with|yield)(?![$\w\xA0-\uFFFF]))(?:[_$A-Za-z\xA0-\uFFFF][$\w\xA0-\uFFFF]*\s*)\(\s*)(?!\s)(?:[^()]|\([^()]*\))+?(?=\s*\)\s*\{)/,lookbehind:!0,inside:x.languages.javascript}],constant:/\b[A-Z](?:[A-Z_]|\dx?)*\b/});x.languages.markup&&x.languages.markup.tag.addInlined("script","javascript");x.languages.js=x.languages.javascript;x.languages.typescript=x.languages.extend("javascript",{keyword:/\b(?:abstract|as|async|await|break|case|catch|class|const|constructor|continue|debugger|declare|default|delete|do|else|enum|export|extends|finally|for|from|function|get|if|implements|import|in|instanceof|interface|is|keyof|let|module|namespace|new|null|of|package|private|protected|public|readonly|return|require|set|static|super|switch|this|throw|try|type|typeof|var|void|while|with|yield)\b/,builtin:/\b(?:string|Function|any|number|boolean|Array|symbol|console|Promise|unknown|never)\b/});x.languages.ts=x.languages.typescript;function le(e,t,r,n,i){this.type=e,this.content=t,this.alias=r,this.length=(n||"").length|0,this.greedy=!!i}le.stringify=function(e,t){return typeof e=="string"?e:Array.isArray(e)?e.map(function(r){return le.stringify(r,t)}).join(""):Zu(e.type)(e.content)};function Zu(e){return Ro[e]||zu}function So(e){return Xu(e,x.languages.javascript)}function Xu(e,t){return x.tokenize(e,t).map(n=>le.stringify(n)).join("")}var ko=B(to());function Oo(e){return(0,ko.default)(e)}var Tr=class e{static read(t){let r;try{r=Io.default.readFileSync(t,"utf-8")}catch{return null}return e.fromContent(r)}static fromContent(t){let r=t.split(/\r?\n/);return new e(1,r)}constructor(t,r){this.firstLineNumber=t,this.lines=r}get lastLineNumber(){return this.firstLineNumber+this.lines.length-1}mapLineAt(t,r){if(t<this.firstLineNumber||t>this.lines.length+this.firstLineNumber)return this;let n=t-this.firstLineNumber,i=[...this.lines];return i[n]=r(i[n]),new e(this.firstLineNumber,i)}mapLines(t){return new e(this.firstLineNumber,this.lines.map((r,n)=>t(r,this.firstLineNumber+n)))}lineAt(t){return this.lines[t-this.firstLineNumber]}prependSymbolAt(t,r){return this.mapLines((n,i)=>i===t?`${r} ${n}`:`  ${n}`)}slice(t,r){let n=this.lines.slice(t-1,r).join(`
`);return new e(t,Oo(n).split(`
`))}highlight(){let t=So(this.toString());return new e(this.firstLineNumber,t.split(`
`))}toString(){return this.lines.join(`
`)}};var ec={red:Ee,gray:bt,dim:De,bold:H,underline:Y,highlightSource:e=>e.highlight()},tc={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function rc({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function nc({callsite:e,message:t,originalMethod:r,isPanic:n,callArguments:i},o){let s=rc({message:t,originalMethod:r,isPanic:n,callArguments:i});if(!e||typeof window<"u"||process.env.NODE_ENV==="production")return s;let a=e.getLocation();if(!a||!a.lineNumber||!a.columnNumber)return s;let l=Math.max(1,a.lineNumber-3),u=Tr.read(a.fileName)?.slice(l,a.lineNumber),c=u?.lineAt(a.lineNumber);if(u&&c){let p=oc(c),d=ic(c);if(!d)return s;s.functionName=`${d.code})`,s.location=a,n||(u=u.mapLineAt(a.lineNumber,g=>g.slice(0,d.openingBraceIndex))),u=o.highlightSource(u);let f=String(u.lastLineNumber).length;if(s.contextLines=u.mapLines((g,h)=>o.gray(String(h).padStart(f))+" "+g).mapLines(g=>o.dim(g)).prependSymbolAt(a.lineNumber,o.bold(o.red("\u2192"))),i){let g=p+f+1;g+=2,s.callArguments=(0,Do.default)(i,g).slice(g)}}return s}function ic(e){let t=Object.keys(Tt.ModelAction).join("|"),n=new RegExp(String.raw`\.(${t})\(`).exec(e);if(n){let i=n.index+n[0].length,o=e.lastIndexOf(" ",n.index)+1;return{code:e.slice(o,i),openingBraceIndex:i}}return null}function oc(e){let t=0;for(let r=0;r<e.length;r++){if(e.charAt(r)!==" ")return t;t++}return t}function sc({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],l=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${l}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${l}`)),t&&a.push(s.underline(ac(t))),i){a.push("");let u=[i.toString()];o&&(u.push(o),u.push(s.dim(")"))),a.push(u.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function ac(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function Cr(e){let t=e.showColors?ec:tc,r;return r=nc(e,t),sc(r,t)}var qo=B(qn());function Fo(e,t,r){let n=Lo(e),i=lc(n),o=cc(i);o?Ar(o,t,r):t.addErrorMessage(()=>"Unknown error")}function Lo(e){return e.errors.flatMap(t=>t.kind==="Union"?Lo(t):[t])}function lc(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:uc(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function uc(e,t){return[...new Set(e.concat(t))]}function cc(e){return _n(e,(t,r)=>{let n=No(t),i=No(r);return n!==i?n-i:Mo(t)-Mo(r)})}function No(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function Mo(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}var re=class{constructor(t,r){this.name=t;this.value=r;this.isRequired=!1}makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};var Ze=class{constructor(t=0,r){this.context=r;this.lines=[];this.currentLine="";this.currentIndent=0;this.currentIndent=t}write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};var Rr=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};var Sr=e=>e,kr={bold:Sr,red:Sr,green:Sr,dim:Sr,enabled:!1},$o={bold:H,red:Ee,green:Et,dim:De,enabled:!0},Xe={write(e){e.writeLine(",")}};var fe=class{constructor(t){this.contents=t;this.isUnderlined=!1;this.color=t=>t}underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};var ke=class{constructor(){this.hasError=!1}markAsError(){return this.hasError=!0,this}};var et=class extends ke{constructor(){super(...arguments);this.items=[]}addItem(r){return this.items.push(new Rr(r)),this}getField(r){return this.items[r]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(n=>n.value.getPrintWidth()))+2}write(r){if(this.items.length===0){this.writeEmpty(r);return}this.writeWithItems(r)}writeEmpty(r){let n=new fe("[]");this.hasError&&n.setColor(r.context.colors.red).underline(),r.write(n)}writeWithItems(r){let{colors:n}=r.context;r.writeLine("[").withIndent(()=>r.writeJoined(Xe,this.items).newLine()).write("]"),this.hasError&&r.afterNextNewline(()=>{r.writeLine(n.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var tt=class e extends ke{constructor(){super(...arguments);this.fields={};this.suggestions=[]}addField(r){this.fields[r.name]=r}addSuggestion(r){this.suggestions.push(r)}getField(r){return this.fields[r]}getDeepField(r){let[n,...i]=r,o=this.getField(n);if(!o)return;let s=o;for(let a of i){let l;if(s.value instanceof e?l=s.value.getField(a):s.value instanceof et&&(l=s.value.getField(Number(a))),!l)return;s=l}return s}getDeepFieldValue(r){return r.length===0?this:this.getDeepField(r)?.value}hasField(r){return!!this.getField(r)}removeAllFields(){this.fields={}}removeField(r){delete this.fields[r]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(r){return this.getField(r)?.value}getDeepSubSelectionValue(r){let n=this;for(let i of r){if(!(n instanceof e))return;let o=n.getSubSelectionValue(i);if(!o)return;n=o}return n}getDeepSelectionParent(r){let n=this.getSelectionParent();if(!n)return;let i=n;for(let o of r){let s=i.value.getFieldValue(o);if(!s||!(s instanceof e))return;let a=s.getSelectionParent();if(!a)return;i=a}return i}getSelectionParent(){let r=this.getField("select")?.value.asObject();if(r)return{kind:"select",value:r};let n=this.getField("include")?.value.asObject();if(n)return{kind:"include",value:n}}getSubSelectionValue(r){return this.getSelectionParent()?.value.fields[r].value}getPrintWidth(){let r=Object.values(this.fields);return r.length==0?2:Math.max(...r.map(i=>i.getPrintWidth()))+2}write(r){let n=Object.values(this.fields);if(n.length===0&&this.suggestions.length===0){this.writeEmpty(r);return}this.writeWithContents(r,n)}asObject(){return this}writeEmpty(r){let n=new fe("{}");this.hasError&&n.setColor(r.context.colors.red).underline(),r.write(n)}writeWithContents(r,n){r.writeLine("{").withIndent(()=>{r.writeJoined(Xe,[...n,...this.suggestions]).newLine()}),r.write("}"),this.hasError&&r.afterNextNewline(()=>{r.writeLine(r.context.colors.red("~".repeat(this.getPrintWidth())))})}};var q=class extends ke{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new fe(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};var Ot=class{constructor(){this.fields=[]}addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(Xe,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function Ar(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":dc(e,t);break;case"IncludeOnScalar":mc(e,t);break;case"EmptySelection":fc(e,t,r);break;case"UnknownSelectionField":wc(e,t);break;case"InvalidSelectionValue":Ec(e,t);break;case"UnknownArgument":bc(e,t);break;case"UnknownInputField":xc(e,t);break;case"RequiredArgumentMissing":vc(e,t);break;case"InvalidArgumentType":Pc(e,t);break;case"InvalidArgumentValue":Tc(e,t);break;case"ValueTooLarge":Cc(e,t);break;case"SomeFieldsMissing":Ac(e,t);break;case"TooManyFieldsGiven":Rc(e,t);break;case"Union":Fo(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function dc(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function mc(e,t){let[r,n]=It(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new re(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${Dt(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function fc(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){gc(e,t,i);return}if(n.hasField("select")){hc(e,t);return}}if(r?.[Ke(e.outputType.name)]){yc(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function gc(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new re(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function hc(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Uo(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${Dt(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function yc(e,t){let r=new Ot;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new re("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=It(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let l=a?.value.asObject()??new tt;l.addSuggestion(n),a.value=l}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function wc(e,t){let r=Bo(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":Uo(n,e.outputType);break;case"include":Sc(n,e.outputType);break;case"omit":kc(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(Dt(n)),i.join(" ")})}function Ec(e,t){let r=Bo(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function bc(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),Oc(n,e.arguments)),t.addErrorMessage(i=>Vo(i,r,e.arguments.map(o=>o.name)))}function xc(e,t){let[r,n]=It(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&Qo(o,e.inputType)}t.addErrorMessage(o=>Vo(o,n,e.inputType.fields.map(s=>s.name)))}function Vo(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=Dc(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(Dt(e)),n.join(" ")}function vc(e,t){let r;t.addErrorMessage(l=>r?.value instanceof q&&r.value.text==="null"?`Argument \`${l.green(o)}\` must not be ${l.red("null")}.`:`Argument \`${l.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=It(e.argumentPath),s=new Ot,a=n.getDeepFieldValue(i)?.asObject();if(a)if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let l of e.inputTypes[0].fields)s.addField(l.name,l.typeNames.join(" | "));a.addSuggestion(new re(o,s).makeRequired())}else{let l=e.inputTypes.map(jo).join(" | ");a.addSuggestion(new re(o,l).makeRequired())}}function jo(e){return e.kind==="list"?`${jo(e.elementType)}[]`:e.name}function Pc(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=Or("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function Tc(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=Or("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function Cc(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof q&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function Ac(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&Qo(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${Or("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(Dt(i)),o.join(" ")})}function Rc(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${Or("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Uo(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new re(r.name,"true"))}function Sc(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new re(r.name,"true"))}function kc(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new re(r.name,"true"))}function Oc(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new re(r.name,r.typeNames.join(" | ")))}function Bo(e,t){let[r,n]=It(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),l=o?.getField(n);return o&&l?{parentKind:"select",parent:o,field:l,fieldName:n}:(l=s?.getField(n),s&&l?{parentKind:"include",field:l,parent:s,fieldName:n}:(l=a?.getField(n),a&&l?{parentKind:"omit",field:l,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function Qo(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new re(r.name,r.typeNames.join(" | ")))}function It(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function Dt({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function Or(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var Ic=3;function Dc(e,t){let r=1/0,n;for(let i of t){let o=(0,qo.default)(e,i);o>Ic||o<r&&(r=o,n=i)}return n}function Go(e){return e.substring(0,1).toLowerCase()+e.substring(1)}var _t=class{constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function rt(e){return e instanceof _t}var Ir=Symbol(),Vn=new WeakMap,Pe=class{constructor(t){t===Ir?Vn.set(this,`Prisma.${this._getName()}`):Vn.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return Vn.get(this)}},Nt=class extends Pe{_getNamespace(){return"NullTypes"}},Mt=class extends Nt{};jn(Mt,"DbNull");var Ft=class extends Nt{};jn(Ft,"JsonNull");var Lt=class extends Nt{};jn(Lt,"AnyNull");var Dr={classes:{DbNull:Mt,JsonNull:Ft,AnyNull:Lt},instances:{DbNull:new Mt(Ir),JsonNull:new Ft(Ir),AnyNull:new Lt(Ir)}};function jn(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}var Jo=": ",_r=class{constructor(t,r){this.name=t;this.value=r;this.hasError=!1}markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+Jo.length}write(t){let r=new fe(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(Jo).write(this.value)}};var Un=class{constructor(t){this.errorMessages=[];this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function nt(e){return new Un(Wo(e))}function Wo(e){let t=new tt;for(let[r,n]of Object.entries(e)){let i=new _r(r,Ho(n));t.addField(i)}return t}function Ho(e){if(typeof e=="string")return new q(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new q(String(e));if(typeof e=="bigint")return new q(`${e}n`);if(e===null)return new q("null");if(e===void 0)return new q("undefined");if(Ye(e))return new q(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return Buffer.isBuffer(e)?new q(`Buffer.alloc(${e.byteLength})`):new q(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=vr(e)?e.toISOString():"Invalid Date";return new q(`new Date("${t}")`)}return e instanceof Pe?new q(`Prisma.${e._getName()}`):rt(e)?new q(`prisma.${Go(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?_c(e):typeof e=="object"?Wo(e):new q(Object.prototype.toString.call(e))}function _c(e){let t=new et;for(let r of e)t.addItem(Ho(r));return t}function Nr(e,t){let r=t==="pretty"?$o:kr,n=e.renderAllMessages(r),i=new Ze(0,{colors:r}).write(e).toString();return{message:n,args:i}}function Mr({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=nt(e);for(let p of t)Ar(p,a,s);let{message:l,args:u}=Nr(a,r),c=Cr({message:l,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:u});throw new J(c,{clientVersion:o})}var ge=class{constructor(){this._map=new Map}get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};function $t(e){let t;return{get(){return t||(t={value:e()}),t.value}}}function he(e){return e.replace(/^./,t=>t.toLowerCase())}function zo(e,t,r){let n=he(r);return!t.result||!(t.result.$allModels||t.result[n])?e:Nc({...e,...Ko(t.name,e,t.result.$allModels),...Ko(t.name,e,t.result[n])})}function Nc(e){let t=new ge,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return Ge(e,n=>({...n,needs:r(n.name,new Set)}))}function Ko(e,t,r){return r?Ge(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:Mc(t,o,i)})):{}}function Mc(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function Yo(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function Zo(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var Fr=class{constructor(t,r){this.extension=t;this.previous=r;this.computedFieldsCache=new ge;this.modelExtensionsCache=new ge;this.queryCallbacksCache=new ge;this.clientExtensions=$t(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());this.batchCallbacks=$t(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t})}getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>zo(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=he(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},it=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new Fr(t))}isEmpty(){return this.head===void 0}append(t){return new e(new Fr(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};var Lr=class{constructor(t){this.name=t}};function Xo(e){return e instanceof Lr}function es(e){return new Lr(e)}var ts=Symbol(),qt=class{constructor(t){if(t!==ts)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?$r:t}},$r=new qt(ts);function ye(e){return e instanceof qt}var Fc={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},rs="explicitly `undefined` values are not allowed";function qr({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=it.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:l,previewFeatures:u,globalOmit:c}){let p=new Bn({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:l,previewFeatures:u,globalOmit:c});return{modelName:e,action:Fc[t],query:Vt(r,p)}}function Vt({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:is(r,n),selection:Lc(e,t,i,n)}}function Lc(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),jc(e,n)):$c(n,t,r)}function $c(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&qc(n,t,e),Vc(n,r,e),n}function qc(e,t,r){for(let[n,i]of Object.entries(t)){if(ye(i))continue;let o=r.nestSelection(n);if(Qn(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=Vt(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=Vt(i,o)}}function Vc(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=Zo(i,n);for(let[s,a]of Object.entries(o)){if(ye(a))continue;Qn(a,r.nestSelection(s));let l=r.findField(s);n?.[s]&&!l||(e[s]=!a)}}function jc(e,t){let r={},n=t.getComputedFields(),i=Yo(e,n);for(let[o,s]of Object.entries(i)){if(ye(s))continue;let a=t.nestSelection(o);Qn(s,a);let l=t.findField(o);if(!(n?.[o]&&!l)){if(s===!1||s===void 0||ye(s)){r[o]=!1;continue}if(s===!0){l?.kind==="object"?r[o]=Vt({},a):r[o]=!0;continue}r[o]=Vt(s,a)}}return r}function ns(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(ze(e)){if(vr(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(Xo(e))return{$type:"Param",value:e.name};if(rt(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return Uc(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:Buffer.from(r,n,i).toString("base64")}}if(Bc(e))return e.values;if(Ye(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof Pe){if(e!==Dr.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(Qc(e))return e.toJSON();if(typeof e=="object")return is(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function is(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);ye(i)||(i!==void 0?r[n]=ns(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:rs}))}return r}function Uc(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||ye(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(ns(o,i))}return r}function Bc(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function Qc(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function Qn(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:rs})}var Bn=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}throwValidationError(t){Mr({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[Ke(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:oe(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};function os(e){if(!e._hasPreviewFlag("metrics"))throw new J("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var ot=class{constructor(t){this._client=t}prometheus(t){return os(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return os(this._client),this._client._engine.metrics({format:"json",...t})}};function ss(e){return{models:Gn(e.models),enums:Gn(e.enums),types:Gn(e.types)}}function Gn(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}function as(e,t){let r=$t(()=>Gc(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function Gc(e){return{datamodel:{models:Jn(e.models),enums:Jn(e.enums),types:Jn(e.types)}}}function Jn(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}var Wn=new WeakMap,Vr="$$PrismaTypedSql",Hn=class{constructor(t,r){Wn.set(this,{sql:t,values:r}),Object.defineProperty(this,Vr,{value:Vr})}get sql(){return Wn.get(this).sql}get values(){return Wn.get(this).values}};function ls(e){return(...t)=>new Hn(e,t)}function us(e){return e!=null&&e[Vr]===Vr}var Ja=B(cs());var Wa=require("async_hooks"),Ha=require("events"),Ka=B(require("fs")),sr=B(require("path"));var X=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let l=0;for(;l<s.values.length;)this.values[o++]=s.values[l++],this.strings[o]=s.strings[l];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function ps(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new X([r,...Array(e.length-1).fill(t),n],e)}function Kn(e){return new X([e],[])}var ds=Kn("");function zn(e,...t){return new X(e,t)}function jt(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}function K(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}function Fe(e){let t=new ge;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}var jr={enumerable:!0,configurable:!0,writable:!0};function Ur(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>jr,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var ms=Symbol.for("nodejs.util.inspect.custom");function ue(e,t){let r=Wc(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=fs(Reflect.ownKeys(o),r),a=fs(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let l=r.get(s);return l?l.getPropertyDescriptor?{...jr,...l?.getPropertyDescriptor(s)}:jr:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[ms]=function(){let o={...this};return delete o[ms],o},i}function Wc(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function fs(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}function st(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}function Br(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}function gs(e){if(e===void 0)return"";let t=nt(e);return new Ze(0,{colors:kr}).write(t).toString()}var Hc="P2037";function Qr({error:e,user_facing_error:t},r,n){return t.error_code?new Q(Kc(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new G(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function Kc(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===Hc&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}var Ut="<unknown>";function hs(e){var t=e.split(`
`);return t.reduce(function(r,n){var i=Zc(n)||ep(n)||np(n)||ap(n)||op(n);return i&&r.push(i),r},[])}var zc=/^\s*at (.*?) ?\(((?:file|https?|blob|chrome-extension|native|eval|webpack|<anonymous>|\/|[a-z]:\\|\\\\).*?)(?::(\d+))?(?::(\d+))?\)?\s*$/i,Yc=/\((\S*)(?::(\d+))(?::(\d+))\)/;function Zc(e){var t=zc.exec(e);if(!t)return null;var r=t[2]&&t[2].indexOf("native")===0,n=t[2]&&t[2].indexOf("eval")===0,i=Yc.exec(t[2]);return n&&i!=null&&(t[2]=i[1],t[3]=i[2],t[4]=i[3]),{file:r?null:t[2],methodName:t[1]||Ut,arguments:r?[t[2]]:[],lineNumber:t[3]?+t[3]:null,column:t[4]?+t[4]:null}}var Xc=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i;function ep(e){var t=Xc.exec(e);return t?{file:t[2],methodName:t[1]||Ut,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var tp=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,rp=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i;function np(e){var t=tp.exec(e);if(!t)return null;var r=t[3]&&t[3].indexOf(" > eval")>-1,n=rp.exec(t[3]);return r&&n!=null&&(t[3]=n[1],t[4]=n[2],t[5]=null),{file:t[3],methodName:t[1]||Ut,arguments:t[2]?t[2].split(","):[],lineNumber:t[4]?+t[4]:null,column:t[5]?+t[5]:null}}var ip=/^\s*(?:([^@]*)(?:\((.*?)\))?@)?(\S.*?):(\d+)(?::(\d+))?\s*$/i;function op(e){var t=ip.exec(e);return t?{file:t[3],methodName:t[1]||Ut,arguments:[],lineNumber:+t[4],column:t[5]?+t[5]:null}:null}var sp=/^\s*at (?:((?:\[object object\])?[^\\/]+(?: \[as \S+\])?) )?\(?(.*?):(\d+)(?::(\d+))?\)?\s*$/i;function ap(e){var t=sp.exec(e);return t?{file:t[2],methodName:t[1]||Ut,arguments:[],lineNumber:+t[3],column:t[4]?+t[4]:null}:null}var Yn=class{getLocation(){return null}},Zn=class{constructor(){this._error=new Error}getLocation(){let t=this._error.stack;if(!t)return null;let n=hs(t).find(i=>{if(!i.file)return!1;let o=Sn(i.file);return o!=="<anonymous>"&&!o.includes("@prisma")&&!o.includes("/packages/client/src/runtime/")&&!o.endsWith("/runtime/binary.js")&&!o.endsWith("/runtime/library.js")&&!o.endsWith("/runtime/edge.js")&&!o.endsWith("/runtime/edge-esm.js")&&!o.startsWith("internal/")&&!i.methodName.includes("new ")&&!i.methodName.includes("getCallSite")&&!i.methodName.includes("Proxy.")&&i.methodName.split(".").length<4});return!n||!n.file?null:{fileName:n.file,lineNumber:n.lineNumber,columnNumber:n.column}}};function Oe(e){return e==="minimal"?typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new Yn:new Zn}var ys={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function at(e={}){let t=up(e);return Object.entries(t).reduce((n,[i,o])=>(ys[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function up(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function Gr(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function ws(e,t){let r=Gr(e);return t({action:"aggregate",unpacker:r,argsMapper:at})(e)}function cp(e={}){let{select:t,...r}=e;return typeof t=="object"?at({...r,_count:t}):at({...r,_count:{_all:!0}})}function pp(e={}){return typeof e.select=="object"?t=>Gr(e)(t)._count:t=>Gr(e)(t)._count._all}function Es(e,t){return t({action:"count",unpacker:pp(e),argsMapper:cp})(e)}function dp(e={}){let t=at(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function mp(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function bs(e,t){return t({action:"groupBy",unpacker:mp(e),argsMapper:dp})(e)}function xs(e,t,r){if(t==="aggregate")return n=>ws(n,r);if(t==="count")return n=>Es(n,r);if(t==="groupBy")return n=>bs(n,r)}function vs(e,t){let r=t.fields.filter(i=>!i.relationName),n=Dn(r,i=>i.name);return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new _t(e,o,s.type,s.isList,s.kind==="enum")},...Ur(Object.keys(n))})}var Ps=e=>Array.isArray(e)?e:e.split("."),Xn=(e,t)=>Ps(t).reduce((r,n)=>r&&r[n],e),Ts=(e,t,r)=>Ps(t).reduceRight((n,i,o,s)=>Object.assign({},Xn(e,s.slice(0,o)),{[i]:n}),r);function fp(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function gp(e,t,r){return t===void 0?e??{}:Ts(t,r,e||!0)}function ei(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((l,u)=>({...l,[u.name]:u}),{});return l=>{let u=Oe(e._errorFormat),c=fp(n,i),p=gp(l,o,c),d=r({dataPath:c,callsite:u})(p),f=hp(e,t);return new Proxy(d,{get(g,h){if(!f.includes(h))return g[h];let v=[a[h].type,r,h],T=[c,p];return ei(e,...v,...T)},...Ur([...f,...Object.getOwnPropertyNames(d)])})}}function hp(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var yp=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],wp=["aggregate","count","groupBy"];function ti(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[Ep(e,t),xp(e,t),jt(r),K("name",()=>t),K("$name",()=>t),K("$parent",()=>e._appliedParent)];return ue({},n)}function Ep(e,t){let r=he(t),n=Object.keys(Tt.ModelAction).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>l=>{let u=Oe(e._errorFormat);return e._createPrismaPromise(c=>{let p={args:l,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:c,callsite:u};return e._request({...p,...a})},{action:o,args:l,model:t})};return yp.includes(o)?ei(e,t,s):bp(i)?xs(e,i,s):s({})}}}function bp(e){return wp.includes(e)}function xp(e,t){return Fe(K("fields",()=>{let r=e._runtimeDataModel.models[t];return vs(t,r)}))}function Cs(e){return e.replace(/^./,t=>t.toUpperCase())}var ri=Symbol();function Bt(e){let t=[vp(e),Pp(e),K(ri,()=>e),K("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(jt(r)),ue(e,t)}function vp(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function Pp(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(he),n=[...new Set(t.concat(r))];return Fe({getKeys(){return n},getPropertyValue(i){let o=Cs(i);if(e._runtimeDataModel.models[o]!==void 0)return ti(e,o);if(e._runtimeDataModel.models[i]!==void 0)return ti(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function As(e){return e[ri]?e[ri]:e}function Rs(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$use:{value:void 0},$on:{value:void 0}});return Bt(t)}function Ss({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let l of Object.values(o)){if(n){if(n[l.name])continue;let u=l.needs.filter(c=>n[c]);u.length>0&&a.push(st(u))}else if(r){if(!r[l.name])continue;let u=l.needs.filter(c=>!r[c]);u.length>0&&a.push(st(u))}Tp(e,l.needs)&&s.push(Cp(l,ue(e,s)))}return s.length>0||a.length>0?ue(e,[...s,...a]):e}function Tp(e,t){return t.every(r=>In(e,r))}function Cp(e,t){return Fe(K(e.name,()=>e.compute(t)))}function Jr({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=Jr({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&ks({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&ks({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function ks({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||ye(s))continue;let l=n.models[r].fields.find(c=>c.name===o);if(!l||l.kind!=="object"||!l.relationName)continue;let u=typeof s=="object"?s:{};t[o]=Jr({visitor:i,result:t[o],args:u,modelName:l.type,runtimeDataModel:n})}}function Os({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:Jr({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,l,u)=>{let c=he(l);return Ss({result:a,modelName:c,select:u.select,omit:u.select?void 0:{...o?.[c],...u.omit},extensions:n})}})}function Is(e){if(e instanceof X)return Ap(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=Qt(e[n]);return r}let t={};for(let r in e)t[r]=Qt(e[r]);return t}function Ap(e){return new X(e.strings,e.values)}function Qt(e){if(typeof e!="object"||e==null||e instanceof Pe||rt(e))return e;if(Ye(e))return new me(e.toFixed());if(ze(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=Qt(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:Qt(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=Qt(e[r]);return t}oe(e,"Unknown value")}function _s(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:Is(t.args??{}),__internalParams:t,query:(s,a=t)=>{let l=a.customDataProxyFetch;return a.customDataProxyFetch=Ls(o,l),a.args=s,_s(e,a,r,n+1)}})})}function Ns(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return _s(e,t,s)}function Ms(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?Fs(r,n,0,e):e(r)}}function Fs(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let l=a.customDataProxyFetch;return a.customDataProxyFetch=Ls(i,l),Fs(a,t,r+1,n)}})}var Ds=e=>e;function Ls(e=Ds,t=Ds){return r=>e(t(r))}var $s=M("prisma:client"),qs={Vercel:"vercel","Netlify CI":"netlify"};function Vs({postinstall:e,ciName:t,clientVersion:r}){if($s("checkPlatformCaching:postinstall",e),$s("checkPlatformCaching:ciName",t),e===!0&&t&&t in qs){let n=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${qs[t]}-build`;throw console.error(n),new S(n,r)}}function js(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}var Rp="Cloudflare-Workers",Sp="node";function Us(){return typeof Netlify=="object"?"netlify":typeof EdgeRuntime=="string"?"edge-light":globalThis.navigator?.userAgent===Rp?"workerd":globalThis.Deno?"deno":globalThis.__lagon__?"lagon":globalThis.process?.release?.name===Sp?"node":globalThis.Bun?"bun":globalThis.fastly?"fastly":"unknown"}var kp={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function Wr(){let e=Us();return{id:e,prettyName:kp[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}var ni=B(On());function Bs(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}function Qs(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}var Gs=B(uo());function Js({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,Gs.default)({user:t,repo:r,template:n,title:e,body:i})}function Ws({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=Vi(6e3-(s?.length??0)),l=Qs((0,ni.default)(a)),u=n?`# Description
\`\`\`
${n}
\`\`\``:"",c=(0,ni.default)(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${process.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${u}

## Logs
\`\`\`
${l}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?Bs(s):""}
\`\`\`
`),p=Js({title:r,body:c});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${Y(p)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}var sa=B(require("node:crypto"),1);var na=e=>{throw TypeError(e)},li=(e,t,r)=>t.has(e)||na("Cannot "+r),Yr=(e,t,r)=>(li(e,t,"read from private field"),r?r.call(e):t.get(e)),Hr=(e,t,r)=>t.has(e)?na("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),ii=(e,t,r,n)=>(li(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),Hs=(e,t,r)=>(li(e,t,"access private method"),r);function ia(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}var Kr="/* prisma-comma-repeatable-start */",oi="/* prisma-comma-repeatable-end */";function Op({query:e,params:t}){if(!e.includes(Kr))return{query:e,params:t};let r=[],n=1,i="",o=0,s=0,a=0;for(;o<e.length;){let l=e[o];if(s===1&&l!=='"'){i+=l,o++;continue}if(l==='"'){s===1?s=a:(a=s,s=1),i+=l,o++;continue}if(e.slice(o,o+Kr.length)===Kr){if(s===2)throw new Error("Nested repetition is not allowed");s=2,o+=Kr.length,i+="(";continue}if(e.slice(o,o+oi.length)===oi){if(s===0)throw new Error("Unmatched repetition end");s=0,o+=oi.length,i+=")";continue}if(l==="$"){let u=e.slice(o+1).match(/^\d+/);if(!u){i+="$",o++;continue}o+=u[0].length+1;let c=parseInt(u[0]),p=t[c-1];switch(s){case 0:{r.push(p),i+=`$${n++}`;break}case 2:{let d=Array.isArray(p)?p:[p];if(d.length===0){i+="NULL";break}d.forEach((f,g)=>{r.push(f),i+=`$${n++}`,g!==d.length-1&&(i+=", ")});break}default:throw new Error(`Unexpected state: ${s}`)}continue}i+=l,o++}return{query:i,params:r}}function Ks({query:e,params:t},r){let n=t.map(a=>{if(!ia(a))return a;let l=r[a.prisma__value.name];if(l===void 0)throw new Error(`Missing value for query variable ${a.prisma__value.name}`);return l}),{query:i,params:o}=Op({query:e,params:n}),s=o.map(a=>Ip(a));return{sql:i,args:o,argTypes:s}}function Ip(e){return e===null?"Int32":typeof e=="string"?"Text":typeof e=="number"?"Numeric":typeof e=="boolean"?"Boolean":Array.isArray(e)?"Array":ia(e)?Dp(e.prisma__value.type):"Json"}function Dp(e){let r={Any:"Json",String:"Text",Int:"Int32",BigInt:"Int64",Float:"Double",Boolean:"Boolean",Decimal:"Numeric",Date:"DateTime",Object:"Json",Bytes:"Bytes",Array:"Array"}[e];if(!r)throw new Error(`Unknown placeholder type: ${e}`);return r}function _p(e){return e.rows.map(t=>t.reduce((r,n,i)=>{let o=e.columnNames[i].split("."),s=r;for(let a=0;a<o.length;a++){let l=o[a];a===o.length-1?s[l]=n:(s[l]===void 0&&(s[l]={}),s=s[l])}return r},{}))}var Jt,Zr,en,Xr,ai,ui=class{constructor({queryable:e,placeholderValues:t,onQuery:r}){Hr(this,Xr),Hr(this,Jt),Hr(this,Zr),Hr(this,en),ii(this,Jt,e),ii(this,Zr,t),ii(this,en,r)}async run(e){return this.interpretNode(e,Yr(this,Zr))}async interpretNode(e,t){switch(e.type){case"seq":{let r=await Promise.all(e.args.map(n=>this.interpretNode(n,t)));return r[r.length-1]}case"get":return t[e.args.name];case"let":{let r=Object.create(t);return await Promise.all(e.args.bindings.map(async n=>{r[n.name]=await this.interpretNode(n.expr,t)})),this.interpretNode(e.args.expr,r)}case"getFirstNonEmpty":{for(let r of e.args.names){let n=t[r];if(!zs(n))return n}return[]}case"concat":return(await Promise.all(e.args.map(n=>this.interpretNode(n,t)))).reduce((n,i)=>n.concat(Np(i)),[]);case"sum":return(await Promise.all(e.args.map(n=>this.interpretNode(n,t)))).reduce((n,i)=>Ys(n)+Ys(i));case"execute":{let r=Ks(e.args,t);return Hs(this,Xr,ai).call(this,r,async()=>{let n=await Yr(this,Jt).executeRaw(r);if(n.ok)return n.value;throw n.error})}case"query":{let r=Ks(e.args,t);return Hs(this,Xr,ai).call(this,r,async()=>{let n=await Yr(this,Jt).queryRaw(r);if(n.ok)return _p(n.value);throw n.error})}case"reverse":{let r=await this.interpretNode(e.args,t);return Array.isArray(r)?r.reverse():r}case"unique":{let r=await this.interpretNode(e.args,t);if(!Array.isArray(r))return r;if(r.length>1)throw new Error(`Expected zero or one element, got ${r.length}`);return r[0]??null}case"required":{let r=await this.interpretNode(e.args,t);if(zs(r))throw new Error("Required value is empty");return r}case"mapField":{let r=await this.interpretNode(e.args.records,t);return oa(r,e.args.field)}case"join":{let r=await this.interpretNode(e.args.parent,t),n=await Promise.all(e.args.children.map(async i=>({joinExpr:i,childRecords:await this.interpretNode(i.child,t)})));if(Array.isArray(r)){for(let i of r)Zs(tn(i),n);return r}return Zs(tn(r),n)}default:throw new Error(`Unexpected node type: ${e.type}`)}}};Jt=new WeakMap;Zr=new WeakMap;en=new WeakMap;Xr=new WeakSet;ai=async function(e,t){var r;let n=new Date,i=performance.now(),o=await t(),s=performance.now();return(r=Yr(this,en))==null||r.call(this,{timestamp:n,duration:s-i,query:e.sql,params:e.args}),o};function zs(e){return Array.isArray(e)?e.length===0:e==null}function Np(e){return Array.isArray(e)?e:[e]}function Ys(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function tn(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function oa(e,t){return Array.isArray(e)?e.map(r=>oa(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function Zs(e,t){for(let{joinExpr:r,childRecords:n}of t)e[r.parentField]=Mp(n,e,r);return e}function Mp(e,t,r){if(Array.isArray(e))return e.filter(n=>Xs(tn(n),t,r));{let n=tn(e);return Xs(n,t,r)?n:null}}function Xs(e,t,r){for(let[n,i]of r.on)if(t[n]!==e[i])return!1;return!0}function Fp(e,t){throw new Error(t)}var ne=class extends Error{constructor(e,t){super("Transaction API error: "+e),this.meta=t,this.code="P2028"}},zr=class extends ne{constructor(e,t){super(`Error from Driver Adapter: ${e}`,{...t.driverAdapterError})}},ea=class extends ne{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},Lp=class extends ne{constructor(e){super(`Transaction already closed: A ${e} cannot be executed on a committed transaction`)}},$p=class extends ne{constructor(e){super(`Transaction already closed: A ${e} cannot be executed on a committed transaction`)}},qp=class extends ne{constructor(){super("Unable to start a transaction in the given time.")}},Vp=class extends ne{constructor(e,{timeout:t,timeTaken:r}){super(`A ${e} cannot be executed on an expired transaction. The timeout for this transaction was ${t} ms, however ${r} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction`,{operation:e,timeout:t,timeTaken:r})}},si=class extends ne{constructor(e){super(`Internal Consistency Error: ${e}`)}},ta=class extends ne{constructor(e){super(`Invalid isolation level: ${e}`,{isolationLevel:e})}},jp=100,Up={ReadUncommitted:"READ UNCOMMITTED",ReadCommitted:"READ COMMITTED",RepeatableRead:"REPEATABLE READ",Snapshot:"SNAPSHOT",Serializable:"SERIALIZABLE"},Gt=M("prisma:client:transactionManager"),Bp=()=>({sql:"COMMIT",args:[],argTypes:[]}),Qp=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),ra=e=>({sql:"SET TRANSACTION ISOLATION LEVEL "+Up[e],args:[],argTypes:[]}),aa=class{constructor({driverAdapter:e}){this.transactions=new Map,this.closedTransactions=[],this.driverAdapter=e}async startTransaction(e){let t=this.validateOptions(e),r={id:sa.default.randomUUID(),status:"waiting",timer:void 0,timeout:t.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(r.id,r),r.timer=this.startTransactionTimeout(r.id,t.maxWait);let n=await this.driverAdapter.transactionContext();if(!n.ok)throw new zr("Failed to start transaction.",{driverAdapterError:n.error});this.requiresSettingIsolationLevelFirst()&&t.isolationLevel&&await n.value.executeRaw(ra(t.isolationLevel));let i=await n.value.startTransaction();if(!i.ok)throw new zr("Failed to start transaction.",{driverAdapterError:i.error});switch(i.value.options.usePhantomQuery||(await i.value.executeRaw({sql:"BEGIN",args:[],argTypes:[]}),!this.requiresSettingIsolationLevelFirst()&&t.isolationLevel&&await n.value.executeRaw(ra(t.isolationLevel))),r.status){case"waiting":return r.transaction=i.value,clearTimeout(r.timer),r.timer=void 0,r.status="running",r.timer=this.startTransactionTimeout(r.id,t.timeout),{id:r.id};case"timed_out":throw new qp;case"running":case"committed":case"rolled_back":throw new si(`Transaction in invalid state ${r.status} although it just finished startup.`);default:Fp(r.status,"Unknown transaction status.")}}async commitTransaction(e){let t=this.getActiveTransaction(e,"commit");await this.closeTransaction(t,"committed")}async rollbackTransaction(e){let t=this.getActiveTransaction(e,"rollback");await this.closeTransaction(t,"rolled_back")}getTransaction(e,t){let r=this.getActiveTransaction(e.id,t);if(!r.transaction)throw new ea;return r.transaction}getActiveTransaction(e,t){let r=this.transactions.get(e);if(!r){let n=this.closedTransactions.find(i=>i.id===e);if(n)switch(Gt("Transaction already closed.",{transactionId:e,status:n.status}),n.status){case"waiting":case"running":throw new si("Active transaction found in closed transactions list.");case"committed":throw new Lp(t);case"rolled_back":throw new $p(t);case"timed_out":throw new Vp(t,{timeout:n.timeout,timeTaken:Date.now()-n.startedAt})}else throw Gt("Transaction not found.",e),new ea}if(["committed","rolled_back","timed_out"].includes(r.status))throw new si("Closed transaction found in active transactions map.");return r}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(e=>this.closeTransaction(e,"rolled_back")))}startTransactionTimeout(e,t){let r=Date.now();return setTimeout(async()=>{Gt("Transaction timed out.",{transactionId:e,timeoutStartedAt:r,timeout:t});let n=this.transactions.get(e);n&&["running","waiting"].includes(n.status)?await this.closeTransaction(n,"timed_out"):Gt("Transaction already committed or rolled back when timeout happened.",e)},t)}async closeTransaction(e,t){if(Gt("Closing transaction.",{transactionId:e.id,status:t}),e.status=t,e.transaction&&t==="committed"){let r=await e.transaction.commit();if(!r.ok)throw new zr("Failed to commit transaction.",{driverAdapterError:r.error});e.transaction.options.usePhantomQuery||await e.transaction.executeRaw(Bp())}else if(e.transaction){let r=await e.transaction.rollback();if(!r.ok)throw new zr("Failed to rollback transaction.",{driverAdapterError:r.error});e.transaction.options.usePhantomQuery||await e.transaction.executeRaw(Qp())}clearTimeout(e.timer),e.timer=void 0,this.transactions.delete(e.id),this.closedTransactions.push(e),this.closedTransactions.length>jp&&this.closedTransactions.shift()}validateOptions(e){if(!e.timeout)throw new ne("timeout is required");if(!e.maxWait)throw new ne("maxWait is required");if(e.isolationLevel==="Snapshot")throw new ta(e.isolationLevel);if(this.driverAdapter.provider==="sqlite"&&e.isolationLevel&&e.isolationLevel!=="Serializable")throw new ta(e.isolationLevel);return{...e,timeout:e.timeout,maxWait:e.maxWait}}requiresSettingIsolationLevelFirst(){return this.driverAdapter.provider==="mysql"}};var ci,la={async loadQueryCompiler(e){let{clientVersion:t,adapter:r,compilerWasm:n}=e;if(r===void 0)throw new S(`The \`adapter\` option for \`PrismaClient\` is required in this context (${Wr().prettyName})`,t);if(n===void 0)throw new S("WASM query compiler was unexpectedly `undefined`",t);return ci===void 0&&(ci=(async()=>{let i=n.getRuntime(),o=await n.getQueryCompilerWasmModule();if(o==null)throw new S("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let s={"./query_compiler_bg.js":i},a=new WebAssembly.Instance(o,s);return i.__wbg_set_wasm(a.exports),i.QueryCompiler})()),await ci}};var ua="P2038",rn=M("prisma:client:clientEngine"),lt,ut=class{constructor(t,r){this.name="ClientEngine";Si(this,lt);if(!t.previewFeatures?.includes("driverAdapters"))throw new S("EngineType `client` requires the driverAdapters preview feature to be enabled.",t.clientVersion,ua);let{adapter:n}=t;if(n)this.driverAdapter=n,rn("Using driver adapter: %O",n);else throw new S("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,ua);this.queryCompilerLoader=r??la,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&ki(this,lt,i=>{this.logEmitter.emit("query",{...i,params:JSON.stringify(i.params),target:"ClientEngine"})}),this.transactionManager=new aa({driverAdapter:this.driverAdapter}),this.instantiateQueryCompilerPromise=this.instantiateQueryCompiler()}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async instantiateQueryCompiler(){if(!this.queryCompiler){this.QueryCompilerConstructor||(this.QueryCompilerConstructor=await this.queryCompilerLoader.loadQueryCompiler(this.config));try{this.queryCompiler=new this.QueryCompilerConstructor({datamodel:this.datamodel,provider:this.driverAdapter.provider,connectionInfo:{}})}catch(t){throw this.transformInitError(t)}}}transformInitError(t){try{let r=JSON.parse(t.message);return new S(r.message,this.config.clientVersion,r.error_code)}catch{return t}}transformRequestError(t){if(t instanceof S)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new se(Gp(this,t.message),this.config.clientVersion);if(t instanceof ne)return new Q(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let r=JSON.parse(t);return new G(`${r.message}
${r.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.instantiateQueryCompilerPromise}async stop(){await this.instantiateQueryCompilerPromise,await this.transactionManager.cancelAllTransactions()}version(){return"unknown"}async transaction(t,r,n){let i;try{if(t==="start"){let o=n;i=await this.transactionManager.startTransaction(o)}else if(t==="commit"){let o=n;await this.transactionManager.commitTransaction(o.id)}else if(t==="rollback"){let o=n;await this.transactionManager.rollbackTransaction(o.id)}else oe(t,"Invalid transaction action.")}catch(o){throw this.transformRequestError(o)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{traceparent:r,interactiveTransaction:n}){rn("sending request");let i=JSON.stringify(t);this.lastStartedQuery=i;try{await this.start();let o=await this.queryCompiler.compile(i),s=JSON.parse(o);rn("query plan created",o);let a=n?this.transactionManager.getTransaction(n,t.action):this.driverAdapter,l={},c=await new ui({queryable:a,placeholderValues:l,onQuery:mn(this,lt)}).run(s);return rn("query plan executed"),{data:{[t.action]:c}}}catch(o){throw this.transformRequestError(o)}}async requestBatch(t,{transaction:r,traceparent:n}){this.lastStartedQuery=JSON.stringify(t);try{await this.start();let i=await Promise.all(t.map(async a=>{let l=JSON.stringify(a),u=await this.queryCompiler.compile(l);return{query:a,plan:JSON.parse(u)}})),o;if(r?.kind==="itx")o=r.options;else{let a=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;o=await this.transaction("start",{},a)}let s=[];for(let{query:a,plan:l}of i){let u=this.transactionManager.getTransaction(o,a.action),c=new ui({queryable:u,placeholderValues:{},onQuery:mn(this,lt)});s.push(await c.run(l))}return r?.kind!=="itx"&&await this.transaction("commit",{},o),s}catch(i){throw this.transformRequestError(i)}}metrics(t){throw new Error("Method not implemented.")}};lt=new WeakMap;function Gp(e,t){return Ws({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:e.lastStartedQuery})}function ct({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw new S(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new S("error: Missing URL environment variable, value, or override.",n);return i}var nn=class extends Error{constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var ee=class extends nn{constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};function C(e,t){return{...e,isRetryable:t}}var pt=class extends ee{constructor(r){super("This request must be retried",C(r,!0));this.name="ForcedRetryError";this.code="P5001"}};b(pt,"ForcedRetryError");var Le=class extends ee{constructor(r,n){super(r,C(n,!1));this.name="InvalidDatasourceError";this.code="P6001"}};b(Le,"InvalidDatasourceError");var $e=class extends ee{constructor(r,n){super(r,C(n,!1));this.name="NotImplementedYetError";this.code="P5004"}};b($e,"NotImplementedYetError");var N=class extends ee{constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var qe=class extends N{constructor(r){super("Schema needs to be uploaded",C(r,!0));this.name="SchemaMissingError";this.code="P5005"}};b(qe,"SchemaMissingError");var pi="This request could not be understood by the server",Wt=class extends N{constructor(r,n,i){super(n||pi,C(r,!1));this.name="BadRequestError";this.code="P5000";i&&(this.code=i)}};b(Wt,"BadRequestError");var Ht=class extends N{constructor(r,n){super("Engine not started: healthcheck timeout",C(r,!0));this.name="HealthcheckTimeoutError";this.code="P5013";this.logs=n}};b(Ht,"HealthcheckTimeoutError");var Kt=class extends N{constructor(r,n,i){super(n,C(r,!0));this.name="EngineStartupError";this.code="P5014";this.logs=i}};b(Kt,"EngineStartupError");var zt=class extends N{constructor(r){super("Engine version is not supported",C(r,!1));this.name="EngineVersionNotSupportedError";this.code="P5012"}};b(zt,"EngineVersionNotSupportedError");var di="Request timed out",Yt=class extends N{constructor(r,n=di){super(n,C(r,!1));this.name="GatewayTimeoutError";this.code="P5009"}};b(Yt,"GatewayTimeoutError");var Jp="Interactive transaction error",Zt=class extends N{constructor(r,n=Jp){super(n,C(r,!1));this.name="InteractiveTransactionError";this.code="P5015"}};b(Zt,"InteractiveTransactionError");var Wp="Request parameters are invalid",Xt=class extends N{constructor(r,n=Wp){super(n,C(r,!1));this.name="InvalidRequestError";this.code="P5011"}};b(Xt,"InvalidRequestError");var mi="Requested resource does not exist",er=class extends N{constructor(r,n=mi){super(n,C(r,!1));this.name="NotFoundError";this.code="P5003"}};b(er,"NotFoundError");var fi="Unknown server error",dt=class extends N{constructor(r,n,i){super(n||fi,C(r,!0));this.name="ServerError";this.code="P5006";this.logs=i}};b(dt,"ServerError");var gi="Unauthorized, check your connection string",tr=class extends N{constructor(r,n=gi){super(n,C(r,!1));this.name="UnauthorizedError";this.code="P5007"}};b(tr,"UnauthorizedError");var hi="Usage exceeded, retry again later",rr=class extends N{constructor(r,n=hi){super(n,C(r,!0));this.name="UsageExceededError";this.code="P5008"}};b(rr,"UsageExceededError");async function Hp(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function nr(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await Hp(e);if(n.type==="QueryEngineError")throw new Q(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new dt(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new qe(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new zt(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new Kt(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new S(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new Ht(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new Zt(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Xt(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new tr(r,mt(gi,n));if(e.status===404)return new er(r,mt(mi,n));if(e.status===429)throw new rr(r,mt(hi,n));if(e.status===504)throw new Yt(r,mt(di,n));if(e.status>=500)throw new dt(r,mt(fi,n));if(e.status>=400)throw new Wt(r,mt(pi,n))}function mt(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}function ca(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}var Te="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function pa(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,l,u,c;for(let p=0;p<o;p=p+3)c=t[p]<<16|t[p+1]<<8|t[p+2],s=(c&16515072)>>18,a=(c&258048)>>12,l=(c&4032)>>6,u=c&63,r+=Te[s]+Te[a]+Te[l]+Te[u];return i==1?(c=t[o],s=(c&252)>>2,a=(c&3)<<4,r+=Te[s]+Te[a]+"=="):i==2&&(c=t[o]<<8|t[o+1],s=(c&64512)>>10,a=(c&1008)>>4,l=(c&15)<<2,r+=Te[s]+Te[a]+Te[l]+"="),r}function da(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new S("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}function Kp(e){return e[0]*1e3+e[1]/1e6}function yi(e){return new Date(Kp(e))}var ma={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.4.0-29.a9055b89e58b4b5bfb59600785423b1db3d0e75d","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};var ir=class extends ee{constructor(r,n){super(`Cannot fetch data from service:
${r}`,C(n,!0));this.name="RequestError";this.code="P5010"}};b(ir,"RequestError");async function Ve(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new ir(a,{clientVersion:n,cause:s})}}var Yp=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,fa=M("prisma:client:dataproxyEngine");async function Zp(e,t){let r=ma["@prisma/engines-version"],n=t.clientVersion??"unknown";if(process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return process.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&Yp.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){if(e.startsWith("localhost")||e.startsWith("127.0.0.1"))return"0.0.0";let[s]=r.split("-")??[],[a,l,u]=s.split("."),c=Xp(`<=${a}.${l}.${u}`),p=await Ve(c,{clientVersion:n});if(!p.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${p.status} ${p.statusText}, response body: ${await p.text()||"<empty body>"}`);let d=await p.text();fa("length of body fetched from unpkg.com",d.length);let f;try{f=JSON.parse(d)}catch(g){throw console.error("JSON.parse error: body fetched from unpkg.com: ",d),g}return f.version}throw new $e("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function ga(e,t){let r=await Zp(e,t);return fa("version",r),r}function Xp(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var ha=3,on=M("prisma:client:dataproxyEngine"),wi=class{constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,interactiveTransaction:r}={}){let n={Authorization:`Bearer ${this.apiKey}`,"Prisma-Engine-Hash":this.engineHash};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-transaction-id"]=r.id);let i=this.buildCaptureSettings();return i.length>0&&(n["X-capture-telemetry"]=i.join(", ")),n}buildCaptureSettings(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}},or=class{constructor(t){this.name="DataProxyEngine";da(t),this.config=t,this.env={...t.env,...typeof process<"u"?process.env:{}},this.inlineSchema=pa(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let[t,r]=this.extractHostAndApiKey();this.host=t,this.headerBuilder=new wi({apiKey:r,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel,logQueries:this.config.logQueries,engineHash:this.engineHash}),this.remoteClientVersion=await ga(t,this.config),on("host",this.host)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":on(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:yi(r.timestamp),message:r.attributes.message??"",target:r.target});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:yi(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`https://${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await Ve(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||on("schema response status",r.status);let n=await nr(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=Br(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(l=>(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l?this.convertProtocolErrorsToClientError(l.errors):l))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await Ve(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,interactiveTransaction:i}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||on("graphql response status",a.status),await this.handleError(await nr(a,this.clientVersion));let l=await a.json();if(l.extensions&&this.propagateResponseExtensions(l.extensions),"errors"in l)throw this.convertProtocolErrorsToClientError(l.errors);return"batchResult"in l?l.batchResult:l}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let l=await Ve(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await nr(l,this.clientVersion));let u=await l.json(),{extensions:c}=u;c&&this.propagateResponseExtensions(c);let p=u.id,d=u["data-proxy"].endpoint;return{id:p,payload:{endpoint:d}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await Ve(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await nr(a,this.clientVersion));let l=await a.json(),{extensions:u}=l;u&&this.propagateResponseExtensions(u);return}}})}extractHostAndApiKey(){let t={clientVersion:this.clientVersion},r=Object.keys(this.inlineDatasources)[0],n=ct({inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources,clientVersion:this.clientVersion,env:this.env}),i;try{i=new URL(n)}catch{throw new Le(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,host:s,searchParams:a}=i;if(o!=="prisma:"&&o!==dr)throw new Le(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t);let l=a.get("api_key");if(l===null||l.length<1)throw new Le(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);return[s,l]}metrics(){throw new $e("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof ee)||!i.isRetryable)throw i;if(r>=ha)throw i instanceof pt?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${ha} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await ca(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof qe)throw await this.uploadSchema(),new pt({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?Qr(t[0],this.config.clientVersion,this.config.activeProvider):new G(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};function ya({copyEngine:e=!0},t){let r;try{r=ct({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...process.env},clientVersion:t.clientVersion})}catch{}let n=!!(r?.startsWith("prisma://")||Rn(r));e&&n&&St("recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)");let i=Qe(t.generator),o=n||!e,s=!!t.adapter,a=i==="library",l=i==="binary",u=i==="client";if(o&&s||s&&!1){let c;throw e?r?.startsWith("prisma://")?c=["Prisma Client was configured to use the `adapter` option but the URL was a `prisma://` URL.","Please either use the `prisma://` URL or remove the `adapter` from the Prisma Client constructor."]:c=["Prisma Client was configured to use both the `adapter` and Accelerate, please chose one."]:c=["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."],new J(c.join(`
`),{clientVersion:t.clientVersion})}return o?new or(t):u?new ut(t):new ut(t)}function sn({generator:e}){return e?.previewFeatures??[]}var wa=e=>({command:e});var Ea=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);function ft(e){try{return ba(e,"fast")}catch{return ba(e,"slow")}}function ba(e,t){return JSON.stringify(e.map(r=>va(r,t)))}function va(e,t){if(Array.isArray(e))return e.map(r=>va(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(ze(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(me.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(Buffer.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(ed(e))return{prisma__type:"bytes",prisma__value:Buffer.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:Buffer.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?Pa(e):e}function ed(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Pa(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(xa);let t={};for(let r of Object.keys(e))t[r]=xa(e[r]);return t}function xa(e){return typeof e=="bigint"?e.toString():Pa(e)}var td=["$connect","$disconnect","$on","$transaction","$use","$extends"],Ta=td;var rd=/^(\s*alter\s)/i,Ca=M("prisma:client");function Ei(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&rd.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var bi=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(us(r))n=r.sql,i={values:ft(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:ft(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:ft(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:ft(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=Ea(r),i={values:ft(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?Ca(`prisma.${e}(${n}, ${i.values})`):Ca(`prisma.${e}(${n})`),{query:n,parameters:i}},Aa={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new X(t,r)}},Ra={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};function xi(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=Sa(r(s)):Sa(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function Sa(e){return typeof e.then=="function"?e:Promise.resolve(e)}var nd=xn.split(".")[0],id={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},vi=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${nd}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??id}};function ka(){return new vi}function Oa(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}function Ia(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}var an=class{constructor(){this._middlewares=[]}use(t){this._middlewares.push(t)}get(t){return this._middlewares[t]}has(t){return!!this._middlewares[t]}length(){return this._middlewares.length}};var _a=B(On());function ln(e){return typeof e.batchRequestIdx=="number"}function Da(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(Pi(e.query.arguments)),t.push(Pi(e.query.selection)),t.join("")}function Pi(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${Pi(n)})`:r}).join(" ")})`}var od={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function Ti(e){return od[e]}var un=class{constructor(t){this.options=t;this.tickActive=!1;this.batches={}}request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,process.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};function je(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=Buffer.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new me(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>je("bigint",r));case"bytes-array":return t.map(r=>je("bytes",r));case"decimal-array":return t.map(r=>je("decimal",r));case"datetime-array":return t.map(r=>je("datetime",r));case"date-array":return t.map(r=>je("date",r));case"time-array":return t.map(r=>je("time",r));default:return t}}function cn(e){let t=[],r=sd(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=je(e.types[s],i[s]);t.push(o)}return t}function sd(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var ad=M("prisma:client:request_handler"),pn=class{constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new un({batchLoader:Ms(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(p=>p.protocolQuery),l=this.client._tracingHelper.getTraceParent(s),u=n.some(p=>Ti(p.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:l,transaction:ld(o),containsWrite:u,customDataProxyFetch:i})).map((p,d)=>{if(p instanceof Error)return p;try{return this.mapQueryEngineResult(n[d],p)}catch(f){return f}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Na(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:Ti(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:Da(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return process.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(ad(t),ud(t,i))throw t;if(t instanceof Q&&cd(t)){let u=Ma(t.meta);Mr({args:o,errors:[u],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let l=t.message;if(n&&(l=Cr({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:l})),l=this.sanitizeMessage(l),t.code){let u=s?{modelName:s,...t.meta}:t.meta;throw new Q(l,{code:t.code,clientVersion:this.client._clientVersion,meta:u,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new se(l,this.client._clientVersion);if(t instanceof G)throw new G(l,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof S)throw new S(l,this.client._clientVersion);if(t instanceof se)throw new se(l,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?(0,_a.default)(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(u=>u!=="select"&&u!=="include"),a=Xn(o,s),l=i==="queryRaw"?cn(a):He(a);return n?n(l):l}get[Symbol.toStringTag](){return"RequestHandler"}};function ld(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:Na(e)};oe(e,"Unknown transaction kind")}}function Na(e){return{id:e.id,payload:e.payload}}function ud(e,t){return ln(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function cd(e){return e.code==="P2009"||e.code==="P2012"}function Ma(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(Ma)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}var Fa="6.4.1";var La=Fa;var Ua=B(qn());var O=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};b(O,"PrismaClientConstructorValidationError");var $a=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],qa=["pretty","colorless","minimal"],Va=["info","query","warn","error"],dd={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new O(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=gt(r,t)||` Available datasources: ${t.join(", ")}`;throw new O(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new O(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new O(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new O(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&Qe(t.generator)==="client")throw new O('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e===null)return;if(e===void 0)throw new O('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(!sn(t).includes("driverAdapters"))throw new O('"adapter" property can only be provided to PrismaClient constructor when "driverAdapters" preview feature is enabled.');if(Qe(t.generator)==="binary")throw new O('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new O(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new O(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!qa.includes(e)){let t=gt(e,qa);throw new O(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new O(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!Va.includes(r)){let n=gt(r,Va);throw new O(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=gt(i,o);throw new O(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new O(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new O(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new O(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new O('"omit" option is expected to be an object.');if(e===null)throw new O('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=fd(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let l=o.fields.find(u=>u.name===s);if(!l){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(l.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new O(gd(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new O(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=gt(r,t);throw new O(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function Ba(e,t){for(let[r,n]of Object.entries(e)){if(!$a.includes(r)){let i=gt(r,$a);throw new O(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}dd[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new O('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function gt(e,t){if(t.length===0||typeof e!="string")return"";let r=md(e,t);return r?` Did you mean "${r}"?`:""}function md(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,Ua.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function fd(e,t){return ja(t.models,e)??ja(t.types,e)}function ja(e,t){let r=Object.keys(e).find(n=>Ke(n)===t);if(r)return e[r]}function gd(e,t){let r=nt(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=Nr(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}function Qa(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},l=u=>{o||(o=!0,r(u))};for(let u=0;u<e.length;u++)e[u].then(c=>{n[u]=c,a()},c=>{if(!ln(c)){l(c);return}c.batchRequestIdx===u?l(c):(i||(i=c),a())})})}var Ie=M("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var hd={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},yd=Symbol.for("prisma.client.transaction.id"),wd={id:0,nextId(){return++this.id}};function za(e){class t{constructor(n){this._originalClient=this;this._middlewares=new an;this._createPrismaPromise=xi();this.$metrics=new ot(this);this.$extends=Rs;e=n?.__internal?.configOverride?.(e)??e,Vs(e),n&&Ba(n,e);let i=new Ha.EventEmitter().on("error",()=>{});this._extensions=it.empty(),this._previewFeatures=sn(e),this._clientVersion=e.clientVersion??La,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=ka();let o={rootEnvPath:e.relativeEnvPaths.rootEnvPath&&sr.default.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&sr.default.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=yn(n.adapter);let l=e.activeProvider==="postgresql"?"postgres":e.activeProvider;if(s.provider!==l)throw new S(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${l}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new S("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=!s&&Pt(o,{conflictCheck:"none"})||e.injectableEdgeEnv?.();try{let l=n??{},u=l.__internal??{},c=u.debug===!0;c&&M.enable("prisma:client");let p=sr.default.resolve(e.dirname,e.relativePath);Ka.default.existsSync(p)||(p=e.dirname),Ie("dirname",e.dirname),Ie("relativePath",e.relativePath),Ie("cwd",p);let d=u.engine||{};if(l.errorFormat?this._errorFormat=l.errorFormat:process.env.NODE_ENV==="production"?this._errorFormat="minimal":process.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:p,dirname:e.dirname,enableDebugLogs:c,allowTriggerPanic:d.allowTriggerPanic,datamodelPath:sr.default.join(e.dirname,e.filename??"schema.prisma"),prismaPath:d.binaryPath??void 0,engineEndpoint:d.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:l.log&&Ia(l.log),logQueries:l.log&&!!(typeof l.log=="string"?l.log==="query":l.log.find(f=>typeof f=="string"?f==="query":f.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:js(l,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:l.transactionOptions?.maxWait??2e3,timeout:l.transactionOptions?.timeout??5e3,isolationLevel:l.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:ct,getBatchRequestPayload:Br,prismaGraphQLToJSError:Qr,PrismaClientUnknownRequestError:G,PrismaClientInitializationError:S,PrismaClientKnownRequestError:Q,debug:M("prisma:client:accelerateEngine"),engineVersion:Ja.version,clientVersion:e.clientVersion}},Ie("clientVersion",e.clientVersion),this._engine=ya(e,this._engineConfig),this._requestHandler=new pn(this,i),l.log)for(let f of l.log){let g=typeof f=="string"?f:f.emit==="stdout"?f.level:null;g&&this.$on(g,h=>{Rt.log(`${Rt.tags[g]??""}`,h.message||h.query)})}}catch(l){throw l.clientVersion=this._clientVersion,l}return this._appliedParent=Bt(this)}get[Symbol.toStringTag](){return"PrismaClient"}$use(n){this._middlewares.use(n)}$on(n,i){n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i)}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{ji()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:bi({clientMethod:i,activeProvider:a}),callsite:Oe(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=Ga(n,i);return Ei(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new J("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(Ei(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new J(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:wa,callsite:Oe(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:bi({clientMethod:i,activeProvider:a}),callsite:Oe(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...Ga(n,i));throw new J("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new J("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=wd.nextId(),s=Oa(n.length),a=n.map((l,u)=>{if(l?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let c=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,p={kind:"batch",id:o,index:u,isolationLevel:c,lock:s};return l.requestTransaction?.(p)??l});return Qa(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),l;try{let u={kind:"itx",...a};l=await n(this._createItxClient(u)),await this._engine.transaction("commit",o,a)}catch(u){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),u}return l}_createItxClient(n){return ue(Bt(ue(As(this),[K("_appliedParent",()=>this._appliedParent._createItxClient(n)),K("_createPrismaPromise",()=>xi(n)),K(yd,()=>n.id)])),[st(Ta)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??hd,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={middleware:{name:"middleware",middleware:!0,attributes:{method:"$use"},active:!1},operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=-1,l=async u=>{let c=this._middlewares.get(++a);if(c)return this._tracingHelper.runInChildSpan(s.middleware,R=>c(u,v=>(R?.end(),l(v))));let{runInTransaction:p,args:d,...f}=u,g={...n,...f};d&&(g.args=i.middlewareArgsToRequestArgs(d)),n.transaction!==void 0&&p===!1&&delete g.transaction;let h=await Ns(this,g);return g.model?Os({result:h,modelName:g.model,args:g.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):h};return this._tracingHelper.runInChildSpan(s.operation,()=>new Wa.AsyncResource("prisma-client-request").runInAsyncScope(()=>l(o)))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:l,argsMapper:u,transaction:c,unpacker:p,otelParentCtx:d,customDataProxyFetch:f}){try{n=u?u(n):n;let g={name:"serialize"},h=this._tracingHelper.runInChildSpan(g,()=>qr({modelName:l,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return M.enabled("prisma:client")&&(Ie("Prisma Client call:"),Ie(`prisma.${i}(${gs(n)})`),Ie("Generated request:"),Ie(JSON.stringify(h,null,2)+`
`)),c?.kind==="batch"&&await c.lock,this._requestHandler.request({protocolQuery:h,modelName:l,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:c,unpacker:p,otelParentCtx:d,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:f})}catch(g){throw g.clientVersion=this._clientVersion,g}}_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}}return t}function Ga(e,t){return Ed(e)?[new X(e,t),Aa]:[e,Ra]}function Ed(e){return Array.isArray(e)&&Array.isArray(e.raw)}var bd=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Ya(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!bd.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}function Za(e){Pt(e,{conflictCheck:"warn"})}0&&(module.exports={Debug,Decimal,Extensions,MetricsClient,PrismaClientInitializationError,PrismaClientKnownRequestError,PrismaClientRustPanicError,PrismaClientUnknownRequestError,PrismaClientValidationError,Public,Sql,createParam,defineDmmfProperty,deserializeJsonResponse,deserializeRawResult,dmmfToRuntimeDataModel,empty,getPrismaClient,getRuntime,join,makeStrictEnum,makeTypedQueryFactory,objectEnumValues,raw,serializeJsonQuery,skip,sqltag,warnEnvConflicts,warnOnce});
/*! Bundled license information:

decimal.js/decimal.mjs:
  (*!
   *  decimal.js v10.5.0
   *  An arbitrary-precision Decimal type for JavaScript.
   *  https://github.com/MikeMcl/decimal.js
   *  Copyright (c) 2025 Michael Mclaughlin <<EMAIL>>
   *  MIT Licence
   *)
*/
//# sourceMappingURL=client.js.map
