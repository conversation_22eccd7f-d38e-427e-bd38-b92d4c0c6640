import { RecordIoType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { ObjectType } from "../core/object-type.mjs";
import { TwinoidUserDisplayName } from "./twinoid-user-display-name.mjs";
import { TwinoidUserId } from "./twinoid-user-id.mjs";
/**
 * A Twinoid user with enough data to display it.
 */
export interface ShortTwinoidUser {
    type: ObjectType.TwinoidUser;
    id: TwinoidUserId;
    displayName: TwinoidUserDisplayName;
}
export declare const $ShortTwinoidUser: RecordIoType<ShortTwinoidUser>;
export type NullableShortTwinoidUser = null | ShortTwinoidUser;
export declare const $NullableShortTwinoidUser: TryUnionType<NullableShortTwinoidUser>;
