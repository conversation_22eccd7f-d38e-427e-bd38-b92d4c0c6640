import { TryUnionType } from "kryo/try-union";
import { Ucs2StringType } from "kryo/ucs2-string";
/**
 * Unique stable OAuth client identifier with the `@clients` suffix.
 */
export type OauthClientKey = string;
export declare const $OauthClientKey: Ucs2StringType;
export type NullableOauthClientKey = null | OauthClientKey;
export declare const $NullableOauthClientKey: TryUnionType<NullableOauthClientKey>;
