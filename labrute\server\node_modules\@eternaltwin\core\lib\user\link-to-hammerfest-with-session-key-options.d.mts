import { RecordIoType } from "kryo/record";
import { HammerfestServer } from "../hammerfest/hammerfest-server.mjs";
import { HammerfestSessionKey } from "../hammerfest/hammerfest-session-key.mjs";
import { LinkToHammerfestMethod } from "./link-to-hammerfest-method.mjs";
import { UserId } from "./user-id.mjs";
export interface LinkToHammerfestWithSessionKeyOptions {
    method: LinkToHammerfestMethod.SessionKey;
    /**
     * Id of the Eternaltwin user to link.
     */
    userId: UserId;
    /**
     * Hammerfest server.
     */
    hammerfestServer: HammerfestServer;
    /**
     * Session key for the Hammerfest user.
     */
    hammerfestSessionKey: HammerfestSessionKey;
}
export declare const $LinkToHammerfestWithSessionKeyOptions: RecordIoType<LinkToHammerfestWithSessionKeyOptions>;
