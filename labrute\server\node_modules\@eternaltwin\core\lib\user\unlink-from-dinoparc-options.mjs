import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $DinoparcServer } from "../dinoparc/dinoparc-server.mjs";
import { $DinoparcUserId } from "../dinoparc/dinoparc-user-id.mjs";
import { $UserId } from "./user-id.mjs";
export const $UnlinkFromDinoparcOptions = new RecordType({
    properties: {
        userId: { type: $UserId },
        dinoparcServer: { type: $DinoparcServer },
        dinoparcUserId: { type: $DinoparcUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=unlink-from-dinoparc-options.mjs.map