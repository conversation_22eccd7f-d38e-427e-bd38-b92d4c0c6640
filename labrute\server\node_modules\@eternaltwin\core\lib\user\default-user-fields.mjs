import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $UserFieldsType, UserFieldsType } from "./user-fields-type.mjs";
export const $DefaultUserFields = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $UserFieldsType, value: UserFieldsType.Default }) },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const DEFAULT_USER_FIELDS = Object.freeze({ type: UserFieldsType.Default });
//# sourceMappingURL=default-user-fields.mjs.map