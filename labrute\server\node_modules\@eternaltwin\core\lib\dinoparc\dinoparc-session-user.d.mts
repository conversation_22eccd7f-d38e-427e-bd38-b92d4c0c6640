import { RecordIoType } from "kryo/record";
import { ShortDinoparcDinozWithLocation } from "./short-dinoparc-dinoz-with-location.mjs";
import { ShortDinoparcUser } from "./short-dinoparc-user.mjs";
export interface DinoparcSessionUser {
    user: ShortDinoparcUser;
    coins: number;
    dinoz: ShortDinoparcDinozWithLocation[];
}
export declare const $DinoparcSessionUser: RecordIoType<DinoparcSessionUser>;
