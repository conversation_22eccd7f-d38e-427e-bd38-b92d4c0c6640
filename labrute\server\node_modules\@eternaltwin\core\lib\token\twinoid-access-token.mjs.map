{"version": 3, "file": "twinoid-access-token.mjs", "sourceRoot": "", "sources": ["../../src/lib/token/twinoid-access-token.mts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,MAAM,MAAM,CAAC;AACjC,OAAO,EAAE,KAAK,EAAE,MAAM,WAAW,CAAC;AAClC,OAAO,EAAE,KAAK,EAAE,MAAM,WAAW,CAAC;AAClC,OAAO,EAAgB,UAAU,EAAE,MAAM,aAAa,CAAC;AACvD,OAAO,EAAE,YAAY,EAAE,MAAM,gBAAgB,CAAC;AAE9C,OAAO,EAAE,uBAAuB,EAA0B,MAAM,yCAAyC,CAAC;AAC1G,OAAO,EAAE,cAAc,EAAiB,MAAM,gCAAgC,CAAC;AAU/E,MAAM,CAAC,MAAM,mBAAmB,GAAqC,IAAI,UAAU,CAAqB;IACtG,UAAU,EAAE;QACV,GAAG,EAAE,EAAC,IAAI,EAAE,uBAAuB,EAAC;QACpC,KAAK,EAAE,EAAC,IAAI,EAAE,KAAK,EAAC;QACpB,KAAK,EAAE,EAAC,IAAI,EAAE,KAAK,EAAC;QACpB,cAAc,EAAE,EAAC,IAAI,EAAE,KAAK,EAAC;QAC7B,aAAa,EAAE,EAAC,IAAI,EAAE,cAAc,EAAC;KACtC;IACD,UAAU,EAAE,SAAS,CAAC,SAAS;CAChC,CAAC,CAAC;AAIH,MAAM,CAAC,MAAM,2BAA2B,GAA6C,IAAI,YAAY,CAAC,EAAC,QAAQ,EAAE,CAAC,KAAK,EAAE,mBAAmB,CAAC,EAAC,CAAC,CAAC"}