import { LiteralUnionType } from "kryo/literal-union";
import { $Null } from "kryo/null";
import { TryUnionType } from "kryo/try-union";
import { $Ucs2String } from "kryo/ucs2-string";
export const $TwinoidLocale = new LiteralUnionType({
    type: $Ucs2String,
    values: ["en", "es", "fr"],
});
export const $NullableTwinoidLocale = new TryUnionType({ variants: [$Null, $TwinoidLocale] });
//# sourceMappingURL=twinoid-locale.mjs.map