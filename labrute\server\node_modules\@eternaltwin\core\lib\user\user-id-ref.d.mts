import { RecordIoType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { ObjectType } from "../core/object-type.mjs";
import { UserId } from "./user-id.mjs";
/**
 * Wrapper object for a user id.
 */
export interface UserIdRef {
    type: ObjectType.User;
    id: UserId;
}
export declare const $UserIdRef: RecordIoType<UserIdRef>;
export type NullableUserIdRef = null | UserIdRef;
export declare const $NullableUserIdRef: TryUnionType<NullableUserIdRef>;
