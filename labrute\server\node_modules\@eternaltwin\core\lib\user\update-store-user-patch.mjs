import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $NullablePasswordHash } from "../password/password-hash.mjs";
import { $UserDisplayName } from "./user-display-name.mjs";
import { $NullableUsername } from "./username.mjs";
export const $UpdateStoreUserPatch = new RecordType({
    properties: {
        displayName: { type: $UserDisplayName, optional: true },
        username: { type: $NullableUsername, optional: true },
        password: { type: $NullablePasswordHash, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=update-store-user-patch.mjs.map