import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $HammerfestServer } from "../hammerfest/hammerfest-server.mjs";
import { $HammerfestUserId } from "../hammerfest/hammerfest-user-id.mjs";
import { $UserId } from "./user-id.mjs";
export const $UnlinkFromHammerfestOptions = new RecordType({
    properties: {
        userId: { type: $UserId },
        hammerfestServer: { type: $HammerfestServer },
        hammerfestUserId: { type: $HammerfestUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=unlink-from-hammerfest-options.mjs.map