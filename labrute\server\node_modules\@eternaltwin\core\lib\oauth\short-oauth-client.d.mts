import { RecordIoType } from "kryo/record";
import { ObjectType } from "../core/object-type.mjs";
import { OauthClientDisplayName } from "./oauth-client-display-name.mjs";
import { OauthClientId } from "./oauth-client-id.mjs";
import { NullableOauthClientKey } from "./oauth-client-key.mjs";
export interface ShortOauthClient {
    type: ObjectType.OauthClient;
    id: OauthClientId;
    key: NullableOauthClientKey;
    displayName: OauthClientDisplayName;
}
export declare const $ShortOauthClient: RecordIoType<ShortOauthClient>;
