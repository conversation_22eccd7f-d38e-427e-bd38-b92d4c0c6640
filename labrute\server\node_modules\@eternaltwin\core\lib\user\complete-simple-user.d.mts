import { RecordIoType } from "kryo/record";
import { FieldVersions } from "../core/field-versions.mjs";
import { NullableDate } from "../core/nullable-date.mjs";
import { ObjectType } from "../core/object-type.mjs";
import { NullableEmailAddress } from "../email/email-address.mjs";
import { UserDisplayName } from "./user-display-name.mjs";
import { UserId } from "./user-id.mjs";
import { NullableUsername } from "./username.mjs";
/**
 * Represents an Eternaltwin user (including private data).
 */
export interface CompleteSimpleUser {
    type: ObjectType.User;
    id: UserId;
    createdAt: Date;
    deletedAt: NullableDate;
    displayName: FieldVersions<UserDisplayName>;
    isAdministrator: boolean;
    username: NullableUsername;
    emailAddress: NullableEmailAddress;
    hasPassword: boolean;
}
export declare const $CompleteSimpleUser: RecordIoType<CompleteSimpleUser>;
