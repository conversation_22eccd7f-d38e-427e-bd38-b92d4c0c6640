import { RecordIoType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { ObjectType } from "../core/object-type.mjs";
import { TwinoidUserDisplayName } from "./twinoid-user-display-name.mjs";
import { TwinoidUserId } from "./twinoid-user-id.mjs";
/**
 * A Twinoid user retrieved from the store.
 */
export interface ArchivedTwinoidUser {
    type: ObjectType.TwinoidUser;
    id: TwinoidUserId;
    archivedAt: Date;
    displayName: TwinoidUserDisplayName;
}
export declare const $ArchivedTwinoidUser: RecordIoType<ArchivedTwinoidUser>;
export type NullableArchivedTwinoidUser = null | ArchivedTwinoidUser;
export declare const $NullableArchivedTwinoidUser: TryUnionType<NullableArchivedTwinoidUser>;
