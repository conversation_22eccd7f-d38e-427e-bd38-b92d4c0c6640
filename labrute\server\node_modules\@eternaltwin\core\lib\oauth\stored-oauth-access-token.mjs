import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { RecordType } from "kryo/record";
import { $UserIdRef } from "../user/user-id-ref.mjs";
import { $OauthClientIdRef } from "./oauth-client-id-ref.mjs";
import { $RfcOauthAccessTokenKey } from "./rfc-oauth-access-token-key.mjs";
export const $StoredOauthAccessToken = new RecordType({
    properties: {
        key: { type: $RfcOauthAccessTokenKey },
        ctime: { type: $Date },
        atime: { type: $Date },
        expirationTime: { type: $Date },
        user: { type: $UserIdRef },
        client: { type: $OauthClientIdRef },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=stored-oauth-access-token.mjs.map