{"version": 3, "file": "DocPlainText.js", "sourceRoot": "", "sources": ["../../src/nodes/DocPlainText.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,qCAA+F;AAE/F,2CAAuD;AAgBvD;;;;;;;GAOG;AACH;IAAkC,gCAAO;IAQvC;;;OAGG;IACH,sBAAmB,UAAmE;QAAtF,YACE,kBAAM,UAAU,CAAC,SAgBlB;QAdC,IAAI,iBAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE;YAC1C,KAAI,CAAC,YAAY,GAAG,IAAI,uBAAU,CAAC;gBACjC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,SAAS;gBAClC,OAAO,EAAE,UAAU,CAAC,WAAW;aAChC,CAAC,CAAC;SACJ;aAAM;YACL,IAAI,YAAY,CAAC,uBAAuB,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,EAAE;gBAC9D,sDAAsD;gBACtD,MAAM,IAAI,KAAK,CAAC,8DAA8D,CAAC,CAAC;aACjF;YAED,KAAI,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC;SAC9B;;IACH,CAAC;IAGD,sBAAW,8BAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,SAAS,CAAC;QAC/B,CAAC;;;OAAA;IAKD,sBAAW,8BAAI;QAHf;;WAEG;aACH;YACE,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE;gBAC5B,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAa,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;aACpD;YACD,OAAO,IAAI,CAAC,KAAK,CAAC;QACpB,CAAC;;;OAAA;IAED,sBAAW,qCAAW;aAAtB;YACE,IAAI,IAAI,CAAC,YAAY,EAAE;gBACrB,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;aAClC;iBAAM;gBACL,OAAO,SAAS,CAAC;aAClB;QACH,CAAC;;;OAAA;IAED,gBAAgB;IACN,sCAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;IAC7B,CAAC;IAxDD,+EAA+E;IAC/E,wCAAwC;IAChB,oCAAuB,GAAW,MAAM,CAAC;IAuDnE,mBAAC;CAAA,AA1DD,CAAkC,iBAAO,GA0DxC;AA1DY,oCAAY", "sourcesContent": ["import { DocNodeKind, IDocNodeParameters, IDocNodeParsedParameters, DocNode } from './DocNode';\r\nimport { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocPlainText}.\r\n */\r\nexport interface IDocPlainTextParameters extends IDocNodeParameters {\r\n  text: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocPlainText}.\r\n */\r\nexport interface IDocPlainTextParsedParameters extends IDocNodeParsedParameters {\r\n  textExcerpt: TokenSequence;\r\n}\r\n\r\n/**\r\n * Represents a span of comment text that is considered by the parser\r\n * to contain no special symbols or meaning.\r\n *\r\n * @remarks\r\n * The text content must not contain newline characters.\r\n * Use DocSoftBreak to represent manual line splitting.\r\n */\r\nexport class DocPlainText extends DocNode {\r\n  // TODO: We should also prohibit \"\\r\", but this requires updating LineExtractor\r\n  // to interpret a lone \"\\r\" as a newline\r\n  private static readonly _newlineCharacterRegExp: RegExp = /[\\n]/;\r\n\r\n  private _text: string | undefined;\r\n  private readonly _textExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocPlainTextParameters | IDocPlainTextParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      this._textExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.PlainText,\r\n        content: parameters.textExcerpt\r\n      });\r\n    } else {\r\n      if (DocPlainText._newlineCharacterRegExp.test(parameters.text)) {\r\n        // Use DocSoftBreak to represent manual line splitting\r\n        throw new Error('The DocPlainText content must not contain newline characters');\r\n      }\r\n\r\n      this._text = parameters.text;\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.PlainText;\r\n  }\r\n\r\n  /**\r\n   * The text content.\r\n   */\r\n  public get text(): string {\r\n    if (this._text === undefined) {\r\n      this._text = this._textExcerpt!.content.toString();\r\n    }\r\n    return this._text;\r\n  }\r\n\r\n  public get textExcerpt(): TokenSequence | undefined {\r\n    if (this._textExcerpt) {\r\n      return this._textExcerpt.content;\r\n    } else {\r\n      return undefined;\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this._textExcerpt];\r\n  }\r\n}\r\n"]}