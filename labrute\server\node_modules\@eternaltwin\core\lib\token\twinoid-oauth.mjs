import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $NullableTwinoidAccessToken } from "./twinoid-access-token.mjs";
import { $NullableTwinoidRefreshToken } from "./twinoid-refresh-token.mjs";
export const $TwinoidOauth = new RecordType({
    properties: {
        accessToken: { type: $NullableTwinoidAccessToken },
        refreshToken: { type: $NullableTwinoidRefreshToken },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=twinoid-oauth.mjs.map