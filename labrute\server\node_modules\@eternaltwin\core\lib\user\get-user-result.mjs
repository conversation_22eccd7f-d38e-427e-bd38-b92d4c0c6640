import { $Null } from "kryo/null";
import { TryUnionType } from "kryo/try-union";
import { $CompleteSimpleUser } from "./complete-simple-user.mjs";
import { $ShortUser } from "./short-user.mjs";
import { $SimpleUser } from "./simple-user.mjs";
export const $GetUserResult = new TryUnionType({
    variants: [$CompleteSimpleUser, $SimpleUser, $ShortUser],
});
export const $NullableGetUserResult = new TryUnionType({ variants: [$Null, $GetUserResult] });
//# sourceMappingURL=get-user-result.mjs.map