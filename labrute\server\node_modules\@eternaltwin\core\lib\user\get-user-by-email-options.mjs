import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { RecordType } from "kryo/record";
import { $EmailAddress } from "../email/email-address.mjs";
export const $GetUserByEmailOptions = new RecordType({
    properties: {
        email: { type: $EmailAddress },
        time: { type: $Date, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=get-user-by-email-options.mjs.map