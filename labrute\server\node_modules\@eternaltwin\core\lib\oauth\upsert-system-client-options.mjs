import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $Url } from "../core/url.mjs";
import { $Password } from "../password/password.mjs";
import { $OauthClientDisplayName } from "./oauth-client-display-name.mjs";
import { $OauthClientKey } from "./oauth-client-key.mjs";
export const $UpsertSystemClientOptions = new RecordType({
    properties: {
        key: { type: $OauthClientKey },
        displayName: { type: $OauthClientDisplayName },
        appUri: { type: $Url },
        callbackUri: { type: $Url },
        secret: { type: $Password },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=upsert-system-client-options.mjs.map