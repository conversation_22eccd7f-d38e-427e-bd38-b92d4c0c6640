import { TaggedUnionType } from "kryo/tagged-union";
import { $LinkToTwinoidWithOauthOptions } from "./link-to-twinoid-with-oauth-options.mjs";
import { $LinkToTwinoidWithRefOptions } from "./link-to-twinoid-with-ref-options.mjs";
export const $LinkToTwinoidOptions = new TaggedUnionType({
    variants: [$LinkToTwinoidWithOauthOptions, $LinkToTwinoidWithRefOptions],
    tag: "method",
});
//# sourceMappingURL=link-to-twinoid-options.mjs.map