import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { $Uint8 } from "kryo/integer";
import { RecordType } from "kryo/record";
import { SetType } from "kryo/set";
import { $PeriodLower } from "../core/period-lower.mjs";
import { $OutboundEmailId } from "./outbound-email-id.mjs";
export const $GetOutboundEmailRequests = new RecordType({
    properties: {
        time: { type: $Date, optional: true },
        createdAt: { type: $PeriodLower, optional: true },
        outboundEmailId: { type: new SetType({ itemType: $OutboundEmailId, maxSize: 100 }), optional: true },
        limit: { type: $Uint8 },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=get-outbound-email-requests.mjs.map