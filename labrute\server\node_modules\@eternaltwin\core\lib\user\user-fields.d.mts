import { TaggedUnionType } from "kryo/tagged-union";
import { CompleteIfSelfUserFields } from "./complete-if-self-user-fields.mjs";
import { CompleteUserFields } from "./complete-user-fields.mjs";
import { DefaultUserFields } from "./default-user-fields.mjs";
import { ShortUserFields } from "./short-user-fields.mjs";
export type UserFields = CompleteIfSelfUserFields | CompleteUserFields | DefaultUserFields | ShortUserFields;
export declare const $UserFields: TaggedUnionType<UserFields>;
