import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { RecordType } from "kryo/record";
import { $Username } from "./username.mjs";
export const $GetUserByUsernameOptions = new RecordType({
    properties: {
        username: { type: $Username },
        time: { type: $Date, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=get-user-by-username-options.mjs.map