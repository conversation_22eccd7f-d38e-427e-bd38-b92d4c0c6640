import { TsEnumType } from "kryo/ts-enum";
/**
 * Tag identifying the Dinoparc linking method.
 */
export var LinkToDinoparcMethod;
(function (LinkToDinoparcMethod) {
    LinkToDinoparcMethod[LinkToDinoparcMethod["Credentials"] = 0] = "Credentials";
    LinkToDinoparcMethod[LinkToDinoparcMethod["Ref"] = 1] = "Ref";
})(LinkToDinoparcMethod || (LinkToDinoparcMethod = {}));
export const $LinkToDinoparcMethod = new TsEnumType({
    enum: LinkToDinoparcMethod,
});
//# sourceMappingURL=link-to-dinoparc-method.mjs.map