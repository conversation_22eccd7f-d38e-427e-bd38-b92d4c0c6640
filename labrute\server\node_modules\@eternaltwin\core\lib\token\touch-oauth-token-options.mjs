import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { RecordType } from "kryo/record";
import { $RfcOauthAccessTokenKey } from "../oauth/rfc-oauth-access-token-key.mjs";
import { $RfcOauthRefreshTokenKey } from "../oauth/rfc-oauth-refresh-token-key.mjs";
import { $TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
export const $TouchOauthTokenOptions = new RecordType({
    properties: {
        accessToken: { type: $RfcOauthAccessTokenKey },
        refreshToken: { type: $RfcOauthRefreshTokenKey, optional: true },
        expirationTime: { type: $Date },
        twinoidUserId: { type: $TwinoidUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=touch-oauth-token-options.mjs.map