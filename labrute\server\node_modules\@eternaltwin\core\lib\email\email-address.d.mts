import { TryUnionType } from "kryo/try-union";
import { Ucs2StringType } from "kryo/ucs2-string";
/**
 * Any email address.
 *
 * It may be non-verified.
 */
export type EmailAddress = string;
/**
 * We only check that the adress is trimmed and non-empty, but leave-out verification.
 * (We only check for the `@` symbol).
 */
export declare const $EmailAddress: Ucs2StringType;
export type NullableEmailAddress = null | EmailAddress;
export declare const $NullableEmailAddress: TryUnionType<NullableEmailAddress>;
