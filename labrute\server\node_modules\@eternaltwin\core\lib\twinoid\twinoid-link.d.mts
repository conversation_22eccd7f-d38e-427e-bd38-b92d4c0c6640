import { RecordIoType } from "kryo/record";
import { LatestTemporal } from "../temporal/latest-temporal.mjs";
import { ShortTwinoidSite } from "./short-twinoid-site.mjs";
import { NullableTwinoidLinkUser } from "./twinoid-link-user.mjs";
export interface TwinoidLink {
    site: ShortTwinoidSite;
    user: LatestTemporal<NullableTwinoidLinkUser>;
}
export declare const $TwinoidLink: RecordIoType<TwinoidLink>;
