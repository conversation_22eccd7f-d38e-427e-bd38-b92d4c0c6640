import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $HammerfestServer } from "../hammerfest/hammerfest-server.mjs";
import { $HammerfestUserId } from "../hammerfest/hammerfest-user-id.mjs";
import { $LinkToHammerfestMethod, LinkToHammerfestMethod } from "./link-to-hammerfest-method.mjs";
import { $UserId } from "./user-id.mjs";
export const $LinkToHammerfestWithRefOptions = new RecordType({
    properties: {
        method: { type: new LiteralType({ type: $LinkToHammerfestMethod, value: LinkToHammerfestMethod.Ref }) },
        userId: { type: $UserId },
        hammerfestServer: { type: $HammerfestServer },
        hammerfestUserId: { type: $HammerfestUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=link-to-hammerfest-with-ref-options.mjs.map