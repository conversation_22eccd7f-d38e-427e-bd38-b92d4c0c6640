import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $FieldShortVersions } from "../core/field-short-versions.mjs";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $NullablePasswordHash } from "../password/password-hash.mjs";
import { $UserDisplayName } from "./user-display-name.mjs";
import { $UserId } from "./user-id.mjs";
export const $ShortUserWithPassword = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.User }) },
        id: { type: $UserId },
        displayName: { type: $FieldShortVersions.apply($UserDisplayName) },
        password: { type: $NullablePasswordHash },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableShortUserWithPassword = new TryUnionType({ variants: [$Null, $ShortUserWithPassword] });
//# sourceMappingURL=short-user-with-password.mjs.map