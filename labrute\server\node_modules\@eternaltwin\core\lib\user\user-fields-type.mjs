import { TsEnumType } from "kryo/ts-enum";
export var UserFieldsType;
(function (UserFieldsType) {
    UserFieldsType[UserFieldsType["CompleteIfSelf"] = 0] = "CompleteIfSelf";
    UserFieldsType[UserFieldsType["Complete"] = 1] = "Complete";
    UserFieldsType[UserFieldsType["Default"] = 2] = "Default";
    UserFieldsType[UserFieldsType["Short"] = 3] = "Short";
})(UserFieldsType || (UserFieldsType = {}));
export const $UserFieldsType = new TsEnumType({
    enum: UserFieldsType,
});
//# sourceMappingURL=user-fields-type.mjs.map