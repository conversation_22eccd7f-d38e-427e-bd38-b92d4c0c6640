import { CaseStyle } from "kryo";
import { ArrayType } from "kryo/array";
import { $Uint32 } from "kryo/integer";
import { RecordType } from "kryo/record";
import { $ShortDinoparcDinozWithLocation } from "./short-dinoparc-dinoz-with-location.mjs";
import { $ShortDinoparcUser } from "./short-dinoparc-user.mjs";
export const $DinoparcSessionUser = new RecordType({
    properties: {
        user: { type: $ShortDinoparcUser },
        coins: { type: $Uint32 },
        dinoz: { type: new ArrayType({ itemType: $ShortDinoparcDinozWithLocation, maxLength: 10000 }) },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=dinoparc-session-user.mjs.map