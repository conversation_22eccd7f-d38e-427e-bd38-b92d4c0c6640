import { CaseStyle } from "kryo";
import { $Any } from "kryo/any";
import { RecordType } from "kryo/record";
import { $NullableDate } from "../core/nullable-date.mjs";
import { $CompleteUser } from "./complete-user.mjs";
export const $EnableUserOut = new RecordType({
    properties: {
        deletedAt: { type: $NullableDate },
        current: { type: $CompleteUser },
        username: { type: $Any },
        dinoparcCom: { type: $Any },
        enDinoparcCom: { type: $Any },
        hammerfestEs: { type: $Any },
        hammerfestFr: { type: $Any },
        hfestNet: { type: $Any },
        spDinoparcCom: { type: $Any },
        twinoid: { type: $Any },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=enable-user-out.mjs.map