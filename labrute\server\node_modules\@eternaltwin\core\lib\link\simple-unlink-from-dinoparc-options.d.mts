import { RecordIoType } from "kryo/record";
import { DinoparcServer } from "../dinoparc/dinoparc-server.mjs";
import { DinoparcUserId } from "../dinoparc/dinoparc-user-id.mjs";
import { UserId } from "../user/user-id.mjs";
export interface SimpleUnlinkFromDinoparcOptions {
    userId: UserId;
    dinoparcServer: DinoparcServer;
    dinoparcUserId: DinoparcUserId;
    unlinkedBy: UserId;
}
export declare const $SimpleUnlinkFromDinoparcOptions: RecordIoType<SimpleUnlinkFromDinoparcOptions>;
