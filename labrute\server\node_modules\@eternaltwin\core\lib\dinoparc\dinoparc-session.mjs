import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $DinoparcSessionKey } from "./dinoparc-session-key.mjs";
import { $ShortDinoparcUser } from "./short-dinoparc-user.mjs";
export const $DinoparcSession = new RecordType({
    properties: {
        ctime: { type: $Date },
        atime: { type: $Date },
        key: { type: $DinoparcSessionKey },
        user: { type: $ShortDinoparcUser },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableDinoparcSession = new TryUnionType({ variants: [$Null, $DinoparcSession] });
//# sourceMappingURL=dinoparc-session.mjs.map