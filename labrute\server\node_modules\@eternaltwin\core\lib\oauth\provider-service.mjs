import { <PERSON><PERSON><PERSON> } from "buffer";
import json<PERSON>ebToken from "jsonwebtoken";
import { NOOP_CONTEXT, readOrThrow } from "kryo";
import { ArrayType } from "kryo/array";
import { $Uint53 } from "kryo/integer";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $Ucs2String } from "kryo/ucs2-string";
import { JSON_VALUE_READER } from "kryo-json/json-value-reader";
import { AuthType } from "../auth/auth-type.mjs";
import { SHORT_USER_FIELDS } from "../user/short-user-fields.mjs";
import { parseScopeString, toOauthClientTypedKey } from "./helpers.mjs";
import { $OauthClientId } from "./oauth-client-id.mjs";
import { OauthTokenType } from "./oauth-token-type.mjs";
export class DefaultOauthProviderService {
    #clock;
    #oauthProviderStore;
    #userStore;
    #tokenSecret;
    #uuidGenerator;
    constructor(options) {
        this.#clock = options.clock;
        this.#oauthProviderStore = options.oauthProviderStore;
        this.#userStore = options.userStore;
        this.#tokenSecret = Buffer.from(options.tokenSecret);
        this.#uuidGenerator = options.uuidGenerator;
    }
    async getClientByIdOrKey(_acx, inputRef) {
        let client;
        if ($OauthClientId.test(NOOP_CONTEXT, inputRef).ok) {
            client = await this.#oauthProviderStore.getClientById(inputRef);
        }
        else {
            client = await this.#oauthProviderStore.getClientByKey(toOauthClientTypedKey(inputRef));
        }
        return client;
    }
    async createOrUpdateSystemClient(key, options) {
        return this.#oauthProviderStore.touchSystemClient({
            key: toOauthClientTypedKey(key),
            displayName: options.displayName,
            appUri: options.appUri,
            callbackUri: options.callbackUri,
            secret: options.secret,
        });
    }
    /**
     * From a user authentication, create an authorization code for the provided client.
     */
    async createAuthorizationCode(auth, clientId, scopeString) {
        const scopes = parseScopeString(scopeString);
        if (auth.type !== AuthType.User) {
            throw new Error("Unauthorized");
        }
        const client = await this.getClientByIdOrKey(auth, clientId);
        if (client === null) {
            throw new Error("ClientNotFound");
        }
        const missingScopes = new Set();
        if (client.owner === null) {
            // System client (authorize all without asking the user).
        }
        else {
            // External client (check missing authorizations).
            for (const scope of scopes) {
                switch (scope) {
                    case "base":
                        throw new Error("NotImplemented: Check if the current user has allowed base access");
                    default:
                        throw new Error(`AssertionError: UnknownScope: ${scope}`);
                }
            }
        }
        if (missingScopes.size > 0) {
            const name = "PromptUserAuthorization";
            const description = `Missing scopes: ${[...missingScopes].join(" ")}`;
            const err = new Error(`${name}: ${description}`);
            err.name = name;
            Reflect.set(err, "missingScopes", missingScopes);
            throw err;
        }
        return this.creatCodeJwt(clientId, client.key, auth.user.id, [...scopes]);
    }
    async createAccessToken(acx, req) {
        if (acx.type !== AuthType.OauthClient) {
            if (acx.type === AuthType.Guest) {
                throw new Error("Unauthorized");
            }
            else {
                throw new Error("Forbidden");
            }
        }
        const codeJwt = await this.readCodeJwt(req.code);
        // TODO: Check if `redirect_uri` matches
        if (!codeJwt.audience.includes(acx.client.id)) {
            throw new Error("Forbidden");
        }
        const key = this.#uuidGenerator.next();
        const ctime = this.#clock.now();
        const expiresIn = 1e9; // TODO: Make it expire!
        const expirationTime = new Date(ctime.getTime() + expiresIn);
        await this.#oauthProviderStore.createAccessToken({
            key, ctime, expirationTime, clientId: acx.client.id, userId: codeJwt.subject,
        });
        return {
            tokenType: OauthTokenType.Bearer,
            accessToken: key,
            expiresIn,
            refreshToken: undefined,
        };
    }
    async getAndTouchAccessToken(_acx, tokenKey) {
        const stored = await this.#oauthProviderStore.getAndTouchAccessTokenByKey(tokenKey);
        if (stored === null) {
            return null;
        }
        return {
            token: {
                tokenType: OauthTokenType.Bearer,
                accessToken: stored.key,
                expiresIn: Math.floor((stored.expirationTime.getTime() / 1000) - this.#clock.nowUnixS()),
                refreshToken: undefined,
            },
            client: stored.client,
            user: stored.user,
        };
    }
    async getAccessTokenByKey(_acx, atKey) {
        // Also update atime
        const storedToken = await this.#oauthProviderStore.getAccessTokenByKey(atKey);
        if (storedToken === null) {
            return null;
        }
        const user = await this.#userStore.getUser({ ref: { id: storedToken.user.id }, fields: SHORT_USER_FIELDS });
        const client = await this.#oauthProviderStore.getClientById(storedToken.client.id);
        if (user === null || client === null) {
            throw new Error("NotFound: User or Client");
        }
        return {
            ...storedToken,
            user,
            client,
        };
    }
    async verifyClientSecret(_acx, clientId, secret) {
        return this.#oauthProviderStore.verifyClientSecret(clientId, secret);
    }
    /**
     * Create the JWT acting as the Oauth authorization code.
     */
    async creatCodeJwt(clientId, clientKey, userId, scopes) {
        const audience = [clientId];
        if (clientKey !== null) {
            audience.push(clientKey);
        }
        return jsonWebToken.sign({ scopes }, this.#tokenSecret, {
            issuer: "etwin",
            subject: userId,
            audience,
            algorithm: "HS256",
            expiresIn: "5min",
        });
    }
    async readCodeJwt(code) {
        const codeObj = jsonWebToken.verify(code, this.#tokenSecret);
        if (typeof codeObj !== "object" || codeObj === null) {
            throw new Error("AssertionError: Expected JWT verification result to be an object");
        }
        return readOrThrow($OauthCodeJwt, JSON_VALUE_READER, codeObj);
    }
}
export const $OauthCodeJwt = new RecordType({
    properties: {
        issuer: { type: new LiteralType({ type: $Ucs2String, value: "etwin" }), rename: "iss" },
        subject: { type: $Ucs2String, rename: "sub" },
        audience: { type: new ArrayType({ itemType: $Ucs2String, maxLength: 2 }), rename: "aud" },
        scopes: { type: new ArrayType({ itemType: $Ucs2String, maxLength: 100 }) },
        issuedAt: { type: $Uint53, rename: "iat" },
        expirationTime: { type: $Uint53, rename: "exp" },
    },
});
//# sourceMappingURL=provider-service.mjs.map