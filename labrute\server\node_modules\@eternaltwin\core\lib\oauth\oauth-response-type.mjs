import { CaseStyle } from "kryo";
import { TsEnumType } from "kryo/ts-enum";
export var OauthResponseType;
(function (OauthResponseType) {
    OauthResponseType[OauthResponseType["Code"] = 0] = "Code";
    OauthResponseType[OauthResponseType["Token"] = 1] = "Token";
})(OauthResponseType || (OauthResponseType = {}));
export const $OauthResponseType = new TsEnumType({
    enum: OauthResponseType,
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=oauth-response-type.mjs.map