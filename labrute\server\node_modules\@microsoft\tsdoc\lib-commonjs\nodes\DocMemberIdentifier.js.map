{"version": 3, "file": "DocMemberIdentifier.js", "sourceRoot": "", "sources": ["../../src/nodes/DocMemberIdentifier.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,qCAA+F;AAC/F,uDAAsD;AAEtD,2CAAuD;AAoBvD;;GAEG;AACH;IAAyC,uCAAO;IAQ9C;;;OAGG;IACH,6BAAmB,UAAiF;QAApG,YACE,kBAAM,UAAU,CAAC,SA2BlB;QAzBC,IAAI,iBAAO,CAAC,kBAAkB,CAAC,UAAU,CAAC,EAAE;YAC1C,IAAI,UAAU,CAAC,gBAAgB,EAAE;gBAC/B,KAAI,CAAC,iBAAiB,GAAG,IAAI,uBAAU,CAAC;oBACtC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,wBAAW,CAAC,0BAA0B;oBACnD,OAAO,EAAE,UAAU,CAAC,gBAAgB;iBACrC,CAAC,CAAC;aACJ;YAED,KAAI,CAAC,kBAAkB,GAAG,IAAI,uBAAU,CAAC;gBACvC,aAAa,EAAE,KAAI,CAAC,aAAa;gBACjC,WAAW,EAAE,wBAAW,CAAC,2BAA2B;gBACpD,OAAO,EAAE,UAAU,CAAC,iBAAiB;aACtC,CAAC,CAAC;YAEH,IAAI,UAAU,CAAC,iBAAiB,EAAE;gBAChC,KAAI,CAAC,kBAAkB,GAAG,IAAI,uBAAU,CAAC;oBACvC,aAAa,EAAE,KAAI,CAAC,aAAa;oBACjC,WAAW,EAAE,wBAAW,CAAC,2BAA2B;oBACpD,OAAO,EAAE,UAAU,CAAC,iBAAiB;iBACtC,CAAC,CAAC;aACJ;SACF;aAAM;YACL,KAAI,CAAC,WAAW,GAAG,UAAU,CAAC,UAAU,CAAC;SAC1C;;IACH,CAAC;IAED;;;;;;;;OAQG;IACW,qCAAiB,GAA/B,UAAgC,UAAkB;QAChD,OAAO,CAAC,2BAAY,CAAC,wCAAwC,CAAC,UAAU,CAAC,CAAC;IAC5E,CAAC;IAGD,sBAAW,qCAAI;QADf,gBAAgB;aAChB;YACE,OAAO,qBAAW,CAAC,gBAAgB,CAAC;QACtC,CAAC;;;OAAA;IASD,sBAAW,2CAAU;QAPrB;;;;;;WAMG;aACH;YACE,IAAI,IAAI,CAAC,WAAW,KAAK,SAAS,EAAE;gBAClC,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,kBAAmB,CAAC,OAAO,CAAC,QAAQ,EAAE,CAAC;aAChE;YACD,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;;;OAAA;IAOD,sBAAW,0CAAS;QALpB;;;;WAIG;aACH;YACE,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBAC3B,OAAO,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC;aACjC;iBAAM;gBACL,OAAO,CAAC,mBAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;aAChE;QACH,CAAC;;;OAAA;IAED,gBAAgB;IACN,6CAAe,GAAzB;QACE,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,CAAC;IACpF,CAAC;IACH,0BAAC;AAAD,CAAC,AA3FD,CAAyC,iBAAO,GA2F/C;AA3FY,kDAAmB", "sourcesContent": ["import { DocNode<PERSON>ind, DocNode, IDocNodeParameters, IDocNodeParsedParameters } from './DocNode';\r\nimport { StringChecks } from '../parser/StringChecks';\r\nimport { TokenSequence } from '../parser/TokenSequence';\r\nimport { DocExcerpt, ExcerptKind } from './DocExcerpt';\r\n\r\n/**\r\n * Constructor parameters for {@link DocMemberIdentifier}.\r\n */\r\nexport interface IDocMemberIdentifierParameters extends IDocNodeParameters {\r\n  identifier: string;\r\n}\r\n\r\n/**\r\n * Constructor parameters for {@link DocMemberIdentifier}.\r\n */\r\nexport interface IDocMemberIdentifierParsedParameters extends IDocNodeParsedParameters {\r\n  leftQuoteExcerpt?: TokenSequence;\r\n\r\n  identifierExcerpt: TokenSequence;\r\n\r\n  rightQuoteExcerpt?: TokenSequence;\r\n}\r\n\r\n/**\r\n * A member identifier is part of a {@link DocMemberReference}.\r\n */\r\nexport class DocMemberIdentifier extends DocNode {\r\n  private readonly _leftQuoteExcerpt: DocExcerpt | undefined;\r\n\r\n  private _identifier: string | undefined;\r\n  private readonly _identifierExcerpt: DocExcerpt | undefined;\r\n\r\n  private readonly _rightQuoteExcerpt: DocExcerpt | undefined;\r\n\r\n  /**\r\n   * Don't call this directly.  Instead use {@link TSDocParser}\r\n   * @internal\r\n   */\r\n  public constructor(parameters: IDocMemberIdentifierParameters | IDocMemberIdentifierParsedParameters) {\r\n    super(parameters);\r\n\r\n    if (DocNode.isParsedParameters(parameters)) {\r\n      if (parameters.leftQuoteExcerpt) {\r\n        this._leftQuoteExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.MemberIdentifier_LeftQuote,\r\n          content: parameters.leftQuoteExcerpt\r\n        });\r\n      }\r\n\r\n      this._identifierExcerpt = new DocExcerpt({\r\n        configuration: this.configuration,\r\n        excerptKind: ExcerptKind.MemberIdentifier_Identifier,\r\n        content: parameters.identifierExcerpt\r\n      });\r\n\r\n      if (parameters.rightQuoteExcerpt) {\r\n        this._rightQuoteExcerpt = new DocExcerpt({\r\n          configuration: this.configuration,\r\n          excerptKind: ExcerptKind.MemberIdentifier_RightQuote,\r\n          content: parameters.rightQuoteExcerpt\r\n        });\r\n      }\r\n    } else {\r\n      this._identifier = parameters.identifier;\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Tests whether the input string can be used without quotes as a member identifier in a declaration reference.\r\n   * If not, {@link DocMemberIdentifier.hasQuotes} will be required.\r\n   *\r\n   * @remarks\r\n   * In order to be used without quotes, the string must follow the identifier syntax for ECMAScript / TypeScript,\r\n   * and it must not be one of the reserved words used for system selectors (such as `instance`, `static`,\r\n   * `constructor`, etc).\r\n   */\r\n  public static isValidIdentifier(identifier: string): boolean {\r\n    return !StringChecks.explainIfInvalidUnquotedMemberIdentifier(identifier);\r\n  }\r\n\r\n  /** @override */\r\n  public get kind(): DocNodeKind | string {\r\n    return DocNodeKind.MemberIdentifier;\r\n  }\r\n\r\n  /**\r\n   * The identifier string without any quote encoding.\r\n   *\r\n   * @remarks\r\n   * If the value is not a valid ECMAScript identifier, it will be quoted as a\r\n   * string literal during rendering.\r\n   */\r\n  public get identifier(): string {\r\n    if (this._identifier === undefined) {\r\n      this._identifier = this._identifierExcerpt!.content.toString();\r\n    }\r\n    return this._identifier;\r\n  }\r\n\r\n  /**\r\n   * Returns true if the identifier will be rendered as a quoted string literal\r\n   * instead of as a programming language identifier.  This is required if the\r\n   * `identifier` property is not a valid ECMAScript identifier.\r\n   */\r\n  public get hasQuotes(): boolean {\r\n    if (this._identifierExcerpt) {\r\n      return !!this._leftQuoteExcerpt;\r\n    } else {\r\n      return !DocMemberIdentifier.isValidIdentifier(this.identifier);\r\n    }\r\n  }\r\n\r\n  /** @override */\r\n  protected onGetChildNodes(): ReadonlyArray<DocNode | undefined> {\r\n    return [this._leftQuoteExcerpt, this._identifierExcerpt, this._rightQuoteExcerpt];\r\n  }\r\n}\r\n"]}