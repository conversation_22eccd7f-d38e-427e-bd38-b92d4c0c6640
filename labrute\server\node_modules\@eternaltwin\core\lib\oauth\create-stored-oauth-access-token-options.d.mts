import { RecordIoType } from "kryo/record";
import { UserId } from "../user/user-id.mjs";
import { OauthClientId } from "./oauth-client-id.mjs";
import { RfcOauthAccessTokenKey } from "./rfc-oauth-access-token-key.mjs";
export interface CreateStoredOauthAccessTokenOptions {
    key: RfcOauthAccessTokenKey;
    ctime: Date;
    expirationTime: Date;
    userId: UserId;
    clientId: OauthClientId;
}
export declare const $StoredOauthAccessToken: RecordIoType<CreateStoredOauthAccessTokenOptions>;
