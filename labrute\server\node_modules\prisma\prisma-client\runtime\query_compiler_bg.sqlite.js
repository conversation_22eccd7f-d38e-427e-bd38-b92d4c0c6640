"use strict";var D=Object.defineProperty;var F=Object.getOwnPropertyDescriptor;var M=Object.getOwnPropertyNames;var O=Object.prototype.hasOwnProperty;var R=(e,t)=>{for(var n in t)D(e,n,{get:t[n],enumerable:!0})},C=(e,t,n,_)=>{if(t&&typeof t=="object"||typeof t=="function")for(let c of M(t))!O.call(e,c)&&c!==n&&D(e,c,{get:()=>t[c],enumerable:!(_=F(t,c))||_.enumerable});return e};var U=e=>C(D({},"__esModule",{value:!0}),e);var qe={};R(qe,{QueryCompiler:()=>Q,__wbg_buffer_b7b08af79b0b0974:()=>ce,__wbg_call_1084a111329e68ce:()=>Ee,__wbg_call_89af060b4e1523f2:()=>me,__wbg_crypto_58f13aa23ffcb166:()=>ae,__wbg_getRandomValues_504510b5564925af:()=>oe,__wbg_getTime_91058879093a1589:()=>Z,__wbg_globalThis_86b222e13bdf32ed:()=>Ie,__wbg_global_e5a3fe56f8be9485:()=>Ae,__wbg_keys_7840ae453e408eab:()=>$,__wbg_length_ae22078168b726f5:()=>ee,__wbg_msCrypto_abcb1295e768d1f2:()=>xe,__wbg_new0_65387337a95cf44d:()=>X,__wbg_new_ea1883e1e5e86686:()=>ue,__wbg_newnoargs_76313bd6ff35d0f2:()=>De,__wbg_newwithbyteoffsetandlength_8a2cb9ca96b27ec9:()=>fe,__wbg_newwithlength_ec548f448387c968:()=>he,__wbg_node_523d7bd03ef69fba:()=>de,__wbg_now_28a6b413aca4a96a:()=>je,__wbg_now_b7a162010a9e75b4:()=>ke,__wbg_process_5b786e71d465a513:()=>be,__wbg_randomFillSync_a0d98aa11c81fe89:()=>se,__wbg_require_2784e593a4674877:()=>we,__wbg_self_3093d5d1f7bcb682:()=>Te,__wbg_set_d1e79e2388520f18:()=>ie,__wbg_set_wasm:()=>V,__wbg_setmessage_698baf5bbc04c9c9:()=>G,__wbg_stringify_bbf45426c92a6bf5:()=>Y,__wbg_subarray_7c2e3576afe181d1:()=>re,__wbg_versions_c2ab80650590b6a2:()=>ge,__wbg_window_3bcfc4d31bc012f8:()=>ve,__wbindgen_error_new:()=>B,__wbindgen_is_function:()=>pe,__wbindgen_is_object:()=>ne,__wbindgen_is_string:()=>le,__wbindgen_is_undefined:()=>J,__wbindgen_memory:()=>_e,__wbindgen_object_clone_ref:()=>H,__wbindgen_object_drop_ref:()=>te,__wbindgen_string_get:()=>K,__wbindgen_string_new:()=>ye,__wbindgen_throw:()=>Se});module.exports=U(qe);var m=()=>{};m.prototype=m;let u;function V(e){u=e}const N=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder;let E=new N("utf-8",{ignoreBOM:!0,fatal:!0});E.decode();let x=null;function h(){return(x===null||x.byteLength===0)&&(x=new Uint8Array(u.memory.buffer)),x}function l(e,t){return e=e>>>0,E.decode(h().subarray(e,e+t))}const g=new Array(128).fill(void 0);g.push(void 0,null,!0,!1);let y=g.length;function o(e){y===g.length&&g.push(g.length+1);const t=y;return y=g[t],g[t]=e,t}function r(e){return g[e]}let T=0;const W=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder;let v=new W("utf-8");const z=typeof v.encodeInto=="function"?function(e,t){return v.encodeInto(e,t)}:function(e,t){const n=v.encode(e);return t.set(n),{read:e.length,written:n.length}};function j(e,t,n){if(n===void 0){const f=v.encode(e),b=t(f.length,1)>>>0;return h().subarray(b,b+f.length).set(f),T=f.length,b}let _=e.length,c=t(_,1)>>>0;const a=h();let i=0;for(;i<_;i++){const f=e.charCodeAt(i);if(f>127)break;a[c+i]=f}if(i!==_){i!==0&&(e=e.slice(i)),c=n(c,_,_=i+e.length*3,1)>>>0;const f=h().subarray(c+i,c+_),b=z(e,f);i+=b.written,c=n(c,_,i,1)>>>0}return T=i,c}function L(e){return e==null}let w=null;function d(){return(w===null||w.buffer.detached===!0||w.buffer.detached===void 0&&w.buffer!==u.memory.buffer)&&(w=new DataView(u.memory.buffer)),w}function P(e){e<132||(g[e]=y,y=e)}function I(e){const t=r(e);return P(e),t}function s(e,t){try{return e.apply(this,t)}catch(n){u.__wbindgen_exn_store(o(n))}}const S=typeof FinalizationRegistry>"u"?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry(e=>u.__wbg_querycompiler_free(e>>>0,1));class Q{__destroy_into_raw(){const t=this.__wbg_ptr;return this.__wbg_ptr=0,S.unregister(this),t}free(){const t=this.__destroy_into_raw();u.__wbg_querycompiler_free(t,0)}constructor(t){try{const a=u.__wbindgen_add_to_stack_pointer(-16);u.querycompiler_new(a,o(t));var n=d().getInt32(a+4*0,!0),_=d().getInt32(a+4*1,!0),c=d().getInt32(a+4*2,!0);if(c)throw I(_);return this.__wbg_ptr=n>>>0,S.register(this,this.__wbg_ptr,this),this}finally{u.__wbindgen_add_to_stack_pointer(16)}}compile(t){let n,_;try{const p=u.__wbindgen_add_to_stack_pointer(-16),k=j(t,u.__wbindgen_malloc,u.__wbindgen_realloc),q=T;u.querycompiler_compile(p,this.__wbg_ptr,k,q);var c=d().getInt32(p+4*0,!0),a=d().getInt32(p+4*1,!0),i=d().getInt32(p+4*2,!0),f=d().getInt32(p+4*3,!0),b=c,A=a;if(f)throw b=0,A=0,I(i);return n=b,_=A,l(b,A)}finally{u.__wbindgen_add_to_stack_pointer(16),u.__wbindgen_free(n,_,1)}}}function B(e,t){const n=new Error(l(e,t));return o(n)}function G(e,t){global.PRISMA_WASM_PANIC_REGISTRY.set_message(l(e,t))}function H(e){const t=r(e);return o(t)}function J(e){return r(e)===void 0}function Y(){return s(function(e){const t=JSON.stringify(r(e));return o(t)},arguments)}function K(e,t){const n=r(t),_=typeof n=="string"?n:void 0;var c=L(_)?0:j(_,u.__wbindgen_malloc,u.__wbindgen_realloc),a=T;d().setInt32(e+4*1,a,!0),d().setInt32(e+4*0,c,!0)}function X(){return o(new Date)}function Z(e){return r(e).getTime()}function $(e){const t=Object.keys(r(e));return o(t)}function ee(e){return r(e).length}function te(e){I(e)}function ne(e){const t=r(e);return typeof t=="object"&&t!==null}function re(e,t,n){const _=r(e).subarray(t>>>0,n>>>0);return o(_)}function oe(){return s(function(e,t){r(e).getRandomValues(r(t))},arguments)}function _e(){const e=u.memory;return o(e)}function ce(e){const t=r(e).buffer;return o(t)}function ue(e){const t=new Uint8Array(r(e));return o(t)}function ie(e,t,n){r(e).set(r(t),n>>>0)}function fe(e,t,n){const _=new Uint8Array(r(e),t>>>0,n>>>0);return o(_)}function se(){return s(function(e,t){r(e).randomFillSync(I(t))},arguments)}function ae(e){const t=r(e).crypto;return o(t)}function be(e){const t=r(e).process;return o(t)}function ge(e){const t=r(e).versions;return o(t)}function de(e){const t=r(e).node;return o(t)}function le(e){return typeof r(e)=="string"}function we(){return s(function(){const e=module.require;return o(e)},arguments)}function pe(e){return typeof r(e)=="function"}function ye(e,t){const n=l(e,t);return o(n)}function me(){return s(function(e,t,n){const _=r(e).call(r(t),r(n));return o(_)},arguments)}function xe(e){const t=r(e).msCrypto;return o(t)}function he(e){const t=new Uint8Array(e>>>0);return o(t)}function Te(){return s(function(){const e=self.self;return o(e)},arguments)}function ve(){return s(function(){const e=window.window;return o(e)},arguments)}function Ie(){return s(function(){const e=globalThis.globalThis;return o(e)},arguments)}function Ae(){return s(function(){const e=global.global;return o(e)},arguments)}function De(e,t){const n=new m(l(e,t));return o(n)}function Ee(){return s(function(e,t){const n=r(e).call(r(t));return o(n)},arguments)}function je(){return s(function(){return Date.now()},arguments)}function Se(e,t){throw new Error(l(e,t))}function ke(){return Date.now()}0&&(module.exports={QueryCompiler,__wbg_buffer_b7b08af79b0b0974,__wbg_call_1084a111329e68ce,__wbg_call_89af060b4e1523f2,__wbg_crypto_58f13aa23ffcb166,__wbg_getRandomValues_504510b5564925af,__wbg_getTime_91058879093a1589,__wbg_globalThis_86b222e13bdf32ed,__wbg_global_e5a3fe56f8be9485,__wbg_keys_7840ae453e408eab,__wbg_length_ae22078168b726f5,__wbg_msCrypto_abcb1295e768d1f2,__wbg_new0_65387337a95cf44d,__wbg_new_ea1883e1e5e86686,__wbg_newnoargs_76313bd6ff35d0f2,__wbg_newwithbyteoffsetandlength_8a2cb9ca96b27ec9,__wbg_newwithlength_ec548f448387c968,__wbg_node_523d7bd03ef69fba,__wbg_now_28a6b413aca4a96a,__wbg_now_b7a162010a9e75b4,__wbg_process_5b786e71d465a513,__wbg_randomFillSync_a0d98aa11c81fe89,__wbg_require_2784e593a4674877,__wbg_self_3093d5d1f7bcb682,__wbg_set_d1e79e2388520f18,__wbg_set_wasm,__wbg_setmessage_698baf5bbc04c9c9,__wbg_stringify_bbf45426c92a6bf5,__wbg_subarray_7c2e3576afe181d1,__wbg_versions_c2ab80650590b6a2,__wbg_window_3bcfc4d31bc012f8,__wbindgen_error_new,__wbindgen_is_function,__wbindgen_is_object,__wbindgen_is_string,__wbindgen_is_undefined,__wbindgen_memory,__wbindgen_object_clone_ref,__wbindgen_object_drop_ref,__wbindgen_string_get,__wbindgen_string_new,__wbindgen_throw});
