import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $OauthClientId } from "./oauth-client-id.mjs";
import { $OauthClientKey } from "./oauth-client-key.mjs";
export const $OauthClientIdRef = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.OauthClient }) },
        id: { type: $OauthClientId },
        key: { type: $OauthClientKey, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=oauth-client-id-ref.mjs.map