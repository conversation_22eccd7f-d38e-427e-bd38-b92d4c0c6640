import { CaseStyle } from "kryo";
import { $Boolean } from "kryo/boolean";
import { $Date } from "kryo/date";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $FieldVersions } from "../core/field-versions.mjs";
import { $NullableDate } from "../core/nullable-date.mjs";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $NullableEmailAddress } from "../email/email-address.mjs";
import { $UserDisplayName } from "./user-display-name.mjs";
import { $UserId } from "./user-id.mjs";
import { $NullableUsername } from "./username.mjs";
export const $CompleteSimpleUser = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.User }) },
        id: { type: $UserId },
        createdAt: { type: $Date },
        deletedAt: { type: $NullableDate },
        displayName: { type: $FieldVersions.apply($UserDisplayName) },
        isAdministrator: { type: $Boolean },
        username: { type: $NullableUsername },
        emailAddress: { type: $NullableEmailAddress },
        hasPassword: { type: $Boolean },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=complete-simple-user.mjs.map