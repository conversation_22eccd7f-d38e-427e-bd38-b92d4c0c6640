import { TryUnionType } from "kryo/try-union";
import { Ucs2StringType } from "kryo/ucs2-string";
/**
 * Unique handle used for authentication. Not all users have a username.
 */
export type Username = string;
/**
 * Can only contain lowercase ascii letters and digits. Must start with a letter.
 */
export declare const $Username: Ucs2StringType;
export type NullableUsername = null | Username;
export declare const $NullableUsername: TryUnionType<NullableUsername>;
