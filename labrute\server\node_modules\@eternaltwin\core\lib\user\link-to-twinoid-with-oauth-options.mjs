import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $OauthAccessToken } from "../oauth/oauth-access-token.mjs";
import { $LinkToTwinoidMethod, LinkToTwinoidMethod } from "./link-to-twinoid-method.mjs";
import { $UserId } from "./user-id.mjs";
export const $LinkToTwinoidWithOauthOptions = new RecordType({
    properties: {
        method: { type: new LiteralType({ type: $LinkToTwinoidMethod, value: LinkToTwinoidMethod.Oauth }) },
        userId: { type: $UserId },
        accessToken: { type: $OauthAccessToken },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=link-to-twinoid-with-oauth-options.mjs.map