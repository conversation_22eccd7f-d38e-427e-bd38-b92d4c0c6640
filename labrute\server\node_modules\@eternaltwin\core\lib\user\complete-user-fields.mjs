import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $UserFieldsType, UserFieldsType } from "./user-fields-type.mjs";
export const $CompleteUserFields = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $UserFieldsType, value: UserFieldsType.Complete }) },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const COMPLETE_USER_FIELDS = Object.freeze({ type: UserFieldsType.Complete });
//# sourceMappingURL=complete-user-fields.mjs.map