import { RecordIoType } from "kryo/record";
import { ObjectType } from "../core/object-type.mjs";
import { DinoparcServer } from "./dinoparc-server.mjs";
import { DinoparcUserId } from "./dinoparc-user-id.mjs";
/**
 * A reference uniquely identifying a Dinoparc user.
 */
export interface DinoparcUserIdRef {
    type: ObjectType.DinoparcUser;
    server: DinoparcServer;
    id: <PERSON>parcUserId;
}
export declare const $DinoparcUserIdRef: RecordIoType<DinoparcUserIdRef>;
