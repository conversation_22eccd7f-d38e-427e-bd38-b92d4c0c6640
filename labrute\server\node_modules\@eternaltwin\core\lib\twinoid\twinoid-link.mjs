import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $LatestTemporal } from "../temporal/latest-temporal.mjs";
import { $ShortTwinoidSite } from "./short-twinoid-site.mjs";
import { $NullableTwinoidLinkUser, } from "./twinoid-link-user.mjs";
export const $TwinoidLink = new RecordType({
    properties: {
        site: { type: $ShortTwinoidSite },
        user: { type: $LatestTemporal.apply($NullableTwinoidLinkUser) },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=twinoid-link.mjs.map