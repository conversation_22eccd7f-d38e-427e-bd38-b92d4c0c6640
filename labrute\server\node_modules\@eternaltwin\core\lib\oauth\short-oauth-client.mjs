import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $OauthClientDisplayName } from "./oauth-client-display-name.mjs";
import { $OauthClientId } from "./oauth-client-id.mjs";
import { $NullableOauthClientKey } from "./oauth-client-key.mjs";
export const $ShortOauthClient = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.OauthClient }) },
        id: { type: $OauthClientId },
        key: { type: $NullableOauthClientKey },
        displayName: { type: $OauthClientDisplayName },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=short-oauth-client.mjs.map