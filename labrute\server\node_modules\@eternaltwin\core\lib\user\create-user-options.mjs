import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $NullableEmailAddress } from "../email/email-address.mjs";
import { $NullablePasswordHash } from "../password/password-hash.mjs";
import { $UserDisplayName } from "./user-display-name.mjs";
import { $NullableUsername } from "./username.mjs";
export const $CreateUserOptions = new RecordType({
    properties: {
        displayName: { type: $UserDisplayName },
        email: { type: $NullableEmailAddress },
        username: { type: $NullableUsername },
        password: { type: $NullablePasswordHash },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=create-user-options.mjs.map