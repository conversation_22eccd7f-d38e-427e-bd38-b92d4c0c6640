import { $Null } from "kryo/null";
import { TryUnionType } from "kryo/try-union";
import { Ucs2StringType } from "kryo/ucs2-string";
export const $DinoparcLocationId = new Ucs2StringType({
    minLength: 1,
    maxLength: 4,
    trimmed: true,
    pattern: /^0|[1-9]\d{0,3}$/,
});
export const $NullableDinoparcLocationId = new TryUnionType({ variants: [$Null, $DinoparcLocationId] });
//# sourceMappingURL=dinoparc-location-id.mjs.map