{"version": 3, "file": "RuleCreator.d.ts", "sourceRoot": "", "sources": ["../../src/eslint-utils/RuleCreator.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,WAAW,EACX,YAAY,EACZ,YAAY,EACZ,gBAAgB,EAChB,UAAU,EACX,MAAM,mBAAmB,CAAC;AAG3B,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,CAAC;AAGzC,MAAM,MAAM,uBAAuB,GAAG,IAAI,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;AACpE,MAAM,MAAM,mBAAmB,CAAC,WAAW,SAAS,MAAM,IAAI,IAAI,CAChE,YAAY,CAAC,WAAW,CAAC,EACzB,MAAM,CACP,GAAG;IACF,IAAI,EAAE,uBAAuB,CAAC;CAC/B,CAAC;AAEF,MAAM,WAAW,oBAAoB,CACnC,QAAQ,SAAS,SAAS,OAAO,EAAE,EACnC,WAAW,SAAS,MAAM;IAE1B,MAAM,EAAE,CACN,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,WAAW,EAAE,QAAQ,CAAC,CAAC,EACrD,kBAAkB,EAAE,QAAQ,CAAC,QAAQ,CAAC,KACnC,YAAY,CAAC;IAClB,cAAc,EAAE,QAAQ,CAAC,QAAQ,CAAC,CAAC;CACpC;AAED,MAAM,WAAW,YAAY,CAC3B,QAAQ,SAAS,SAAS,OAAO,EAAE,EACnC,WAAW,SAAS,MAAM,CAC1B,SAAQ,oBAAoB,CAAC,QAAQ,EAAE,WAAW,CAAC;IACnD,IAAI,EAAE,YAAY,CAAC,WAAW,CAAC,CAAC;CACjC;AAED,MAAM,WAAW,mBAAmB,CAClC,QAAQ,SAAS,SAAS,OAAO,EAAE,EACnC,WAAW,SAAS,MAAM,CAC1B,SAAQ,oBAAoB,CAAC,QAAQ,EAAE,WAAW,CAAC;IACnD,IAAI,EAAE,mBAAmB,CAAC,WAAW,CAAC,CAAC;IACvC,IAAI,EAAE,MAAM,CAAC;CACd;AAED;;;;;GAKG;AACH,wBAAgB,WAAW,CAAC,UAAU,EAAE,CAAC,QAAQ,EAAE,MAAM,KAAK,MAAM,uMAyBnE;yBAzBe,WAAW;;;AA2B3B;;;;;GAKG;AACH,iBAAS,UAAU,CACjB,QAAQ,SAAS,SAAS,OAAO,EAAE,EACnC,WAAW,SAAS,MAAM,EAC1B,EACA,MAAM,EACN,cAAc,EACd,IAAI,GACL,EAAE,QAAQ,CAAC,YAAY,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC,GAAG,UAAU,CAC3D,WAAW,EACX,QAAQ,CACT,CAWA"}