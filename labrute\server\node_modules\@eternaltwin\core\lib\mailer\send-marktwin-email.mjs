import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { RecordType } from "kryo/record";
import { Ucs2StringType } from "kryo/ucs2-string";
import { $UuidHex } from "kryo/uuid-hex";
import { $EmailAddress } from "../email/email-address.mjs";
export const $SendMarktwinEmail = new RecordType({
    properties: {
        deadline: { type: $Date },
        idempotencyKey: { type: $UuidHex },
        recipient: { type: $EmailAddress },
        subject: { type: new Ucs2StringType({ minLength: 1, maxLength: 20, trimmed: true }) },
        body: { type: new Ucs2StringType({ minLength: 1, maxLength: 1000, trimmed: true }) },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=send-marktwin-email.mjs.map