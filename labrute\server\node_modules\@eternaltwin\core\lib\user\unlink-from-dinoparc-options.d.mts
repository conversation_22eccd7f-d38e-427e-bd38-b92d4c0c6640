import { RecordIoType } from "kryo/record";
import { DinoparcServer } from "../dinoparc/dinoparc-server.mjs";
import { DinoparcUserId } from "../dinoparc/dinoparc-user-id.mjs";
import { UserId } from "./user-id.mjs";
export interface UnlinkFromDinoparcOptions {
    /**
     * Id of the Eternaltwin user to link.
     */
    userId: UserId;
    /**
     * Dinoparc server.
     */
    dinoparcServer: DinoparcServer;
    /**
     * User id for the Dinoparc user.
     */
    dinoparcUserId: DinoparcUserId;
}
export declare const $UnlinkFromDinoparcOptions: RecordIoType<UnlinkFromDinoparcOptions>;
