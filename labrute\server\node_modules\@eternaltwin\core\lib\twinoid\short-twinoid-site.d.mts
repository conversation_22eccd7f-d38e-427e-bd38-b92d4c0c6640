import { RecordIoType } from "kryo/record";
import { ObjectType } from "../core/object-type.mjs";
import { NullableTwinoidSiteHost } from "./twinoid-site-host.mjs";
import { TwinoidSiteId } from "./twinoid-site-id.mjs";
export interface ShortTwinoidSite {
    type: ObjectType.TwinoidSite;
    id: TwinoidSiteId;
    host: NullableTwinoidSiteHost;
}
export declare const $ShortTwinoidSite: RecordIoType<ShortTwinoidSite>;
