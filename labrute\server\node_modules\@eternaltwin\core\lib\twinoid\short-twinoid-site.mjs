import { CaseStyle } from "kryo";
import { LiteralType } from "kryo/literal";
import { RecordType } from "kryo/record";
import { $ObjectType, ObjectType } from "../core/object-type.mjs";
import { $NullableTwinoidSiteHost } from "./twinoid-site-host.mjs";
import { $TwinoidSiteId } from "./twinoid-site-id.mjs";
export const $ShortTwinoidSite = new RecordType({
    properties: {
        type: { type: new LiteralType({ type: $ObjectType, value: ObjectType.TwinoidSite }) },
        id: { type: $TwinoidSiteId },
        host: { type: $NullableTwinoidSiteHost },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=short-twinoid-site.mjs.map