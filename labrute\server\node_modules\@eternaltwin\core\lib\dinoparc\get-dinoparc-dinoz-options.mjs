import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { RecordType } from "kryo/record";
import { $DinoparcDinozId } from "./dinoparc-dinoz-id.mjs";
import { $DinoparcServer } from "./dinoparc-server.mjs";
export const $GetDinoparcDinozOptions = new RecordType({
    properties: {
        server: { type: $DinoparcServer },
        id: { type: $DinoparcDinozId },
        time: { type: $Date, optional: true },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=get-dinoparc-dinoz-options.mjs.map