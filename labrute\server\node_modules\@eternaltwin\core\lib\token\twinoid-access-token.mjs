import { CaseStyle } from "kryo";
import { $Date } from "kryo/date";
import { $Null } from "kryo/null";
import { RecordType } from "kryo/record";
import { TryUnionType } from "kryo/try-union";
import { $RfcOauthAccessTokenKey } from "../oauth/rfc-oauth-access-token-key.mjs";
import { $TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
export const $TwinoidAccessToken = new RecordType({
    properties: {
        key: { type: $RfcOauthAccessTokenKey },
        ctime: { type: $Date },
        atime: { type: $Date },
        expirationTime: { type: $Date },
        twinoidUserId: { type: $TwinoidUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
export const $NullableTwinoidAccessToken = new TryUnionType({ variants: [$Null, $TwinoidAccessToken] });
//# sourceMappingURL=twinoid-access-token.mjs.map