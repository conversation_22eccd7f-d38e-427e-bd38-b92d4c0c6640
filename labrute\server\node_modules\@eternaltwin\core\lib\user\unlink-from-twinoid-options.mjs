import { CaseStyle } from "kryo";
import { RecordType } from "kryo/record";
import { $TwinoidUserId } from "../twinoid/twinoid-user-id.mjs";
import { $UserId } from "./user-id.mjs";
export const $UnlinkFromTwinoidOptions = new RecordType({
    properties: {
        userId: { type: $UserId },
        twinoidUserId: { type: $TwinoidUserId },
    },
    changeCase: CaseStyle.SnakeCase,
});
//# sourceMappingURL=unlink-from-twinoid-options.mjs.map